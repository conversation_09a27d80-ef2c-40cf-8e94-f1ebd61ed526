import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/old_models/seller_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:map_launcher/map_launcher.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';

import '../../utils/colors.dart';

class SellerInfoDialog extends StatefulWidget {
  final String sellerId;

  const SellerInfoDialog({
    required this.sellerId,
    Key? key,
  }) : super(key: key);

  @override
  _SellerInfoDialogState createState() => _SellerInfoDialogState();
}

class _SellerInfoDialogState extends State<SellerInfoDialog> {
  SellerInfo? sellerInfoData;
  bool error = false;
  double lat = 0;
  double lng = 0;
  MapboxMap? mapboxMap;

  @override
  void initState() {
    super.initState();
    fetchApi();
  }

  Future<void> fetchApi() async {
    try {
      var result =
          await NetworkController().getSellerinformation(widget.sellerId);
      setState(() {
        sellerInfoData = result;
        error = false;
        lat = sellerInfoData?.data?.latitude ?? 0;
        lng = sellerInfoData?.data?.longitude ?? 0;
      });
    } catch (e) {
      setState(() {
        error = true;
      });
    }
  }

  void _onMapCreated(MapboxMap mapboxMap1) {
    mapboxMap = mapboxMap1;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.all(10),
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      child: error ? _buildErrorDialog() : _buildInfoDialog(),
    );
  }

  Widget _buildErrorDialog() {
    return Container(
      color: Colors.white,
      width: 335,
      height: 550,
      child: const Center(
        child: Text("This seller's information is unavailable!"),
      ),
    );
  }

  Widget _buildInfoDialog() {
    return Stack(
      children: <Widget>[
        Container(
          width: 335,
          height: 550,
          padding:
              const EdgeInsets.only(left: 20, top: 65, right: 20, bottom: 20),
          margin: const EdgeInsets.only(top: 45),
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: const [
              BoxShadow(
                  color: Colors.black, offset: Offset(0, 10), blurRadius: 10),
            ],
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  "Seller Info ${sellerInfoData?.data?.name ?? ""}",
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                _buildPersonalInfo(),
                const SizedBox(height: 16),
                _buildMap(),
              ],
            ),
          ),
        ),
        Positioned(
          left: 20,
          right: 20,
          child: CircleAvatar(
            radius: 48,
            backgroundColor: Colors.grey.shade200,
            child: const CircleAvatar(
              backgroundColor: Colors.white,
              radius: 44,
              child: Icon(
                Icons.info,
                color: Colors.green,
                size: 60.0,
              ),
            ),
          ),
        ),
        Positioned(
          top: 0,
          right: 0,
          child: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: const Icon(
              Icons.cancel,
              color: AppColors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPersonalInfo() {
    return Column(
      children: [
        const SizedBox(height: 12),
        _buildInfoRow(Icons.perm_contact_cal_rounded, "Name",
            sellerInfoData?.data?.name ?? ""),
        const SizedBox(height: 20),
        _buildInfoRow(Icons.my_location_outlined, "Address", _getAddress()),
        const SizedBox(height: 20),
        _buildInfoRow(Icons.email, "Email", sellerInfoData?.data?.email ?? ""),
        const SizedBox(height: 20),
        _buildInfoRow(Icons.phone, "Phone",
            "${(double.parse(sellerInfoData?.data?.phoneCountryCode ?? "0").toStringAsFixed(0))}${sellerInfoData?.data?.phone ?? ""}"),
        const SizedBox(height: 20),
        _buildInfoRow(Icons.star, "Rating", "", isRating: true),
        const SizedBox(height: 20),
        _buildInfoRow(Icons.location_on, "Location",
            sellerInfoData?.data?.location ?? ""),
      ],
    );
  }

  String _getAddress() {
    return "${sellerInfoData?.data?.address?.sellingAddressLine1 ?? ""} ${sellerInfoData?.data?.address?.sellingAddressLine2 ?? ""} ${sellerInfoData?.data?.address?.city ?? ""} ${sellerInfoData?.data?.address?.state ?? ""} ${sellerInfoData?.data?.address?.country ?? ""} ${sellerInfoData?.data?.address?.pincode ?? ""}";
  }

  Widget _buildInfoRow(IconData icon, String label, String value,
      {bool isRating = false}) {
    return Row(
      children: [
        Icon(icon, color: AppColors.green, size: 24.0),
        const SizedBox(width: 15),
        SizedBox(
          width: 90,
          child: Text(label,
              style: const TextStyle(
                  color: Colors.black, fontWeight: FontWeight.bold)),
        ),
        const Text(":"),
        const SizedBox(width: 2),
        SizedBox(
          width: 150,
          child: isRating
              ? RatingBarIndicator(
                  rating: double.parse(sellerInfoData?.data?.rating ?? "0"),
                  itemBuilder: (context, index) =>
                      const Icon(Icons.star, color: Colors.amber),
                  itemCount: 5,
                  itemSize: 18.0,
                  unratedColor: Colors.grey,
                  direction: Axis.horizontal,
                )
              : Text(value,
                  style: const TextStyle(
                      color: Colors.black, fontWeight: FontWeight.bold)),
        ),
      ],
    );
  }

  Widget _buildMap() {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white, border: Border.all(color: Colors.white)),
      height: 150,
      child: lat != 0
          ? MapWidget(
              key: const ValueKey("mapWidget"),
              onTapListener: (onTapListener) async {
                final availableMaps =
                    await MapLauncher.isMapAvailable(MapType.google);
                if (availableMaps == true) {
                  await MapLauncher.showMarker(
                    mapType: MapType.google,
                    coords: Coords(lat, lng),
                    title: "Seller Location",
                  );
                } else {
                  await MapLauncher.showMarker(
                    mapType: MapType.apple,
                    coords: Coords(lat, lng),
                    title: "Seller Location",
                  );
                }
              },
              onMapCreated: (onMapCreated) {
                _onMapCreated(onMapCreated);
                mapboxMap?.annotations
                    .createPointAnnotationManager()
                    .then((pointAnnotationManager) async {
                  final ByteData bytes =
                      await rootBundle.load('assets/images/ic_marker.png');
                  final Uint8List marker = bytes.buffer.asUint8List();
                  var options = <PointAnnotationOptions>[];
                  options.add(PointAnnotationOptions(
                      geometry: Point(coordinates: Position(lng, lat)).toJson(),
                      image: marker,
                      iconSize: Platform.isIOS ? 1 : 3));
                  pointAnnotationManager.createMulti(options);
                });
              },
              resourceOptions: ResourceOptions(
                  accessToken:
                      "pk.eyJ1IjoiY29ubmVjdG9uZWNsdWIiLCJhIjoiY2txMjd3bjl6MDZicjJvczNrbXhmaWVzbSJ9.unLFJ-b5u7-OHlSplUJWrw"),
              cameraOptions: CameraOptions(
                  center: Point(coordinates: Position(lng, lat)).toJson(),
                  zoom: 15.0),
            )
          : const Center(child: Text("Location not available now")),
    );
  }
}
