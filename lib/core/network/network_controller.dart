import 'dart:convert';
import 'dart:io';

import 'package:connectone/bai_models/add_employee_req.dart';
import 'package:connectone/bai_models/add_seller_req.dart';
import 'package:connectone/bai_models/allow_edit_res.dart';
import 'package:connectone/bai_models/calculator_res.dart';
import 'package:connectone/bai_models/customers_list_res.dart';
import 'package:connectone/bai_models/round_off_discount_req.dart';
import 'package:connectone/bai_models/round_off_discount_res.dart';
import 'package:connectone/bai_models/accept_negotiation_req.dart';
import 'package:connectone/bai_models/add_project_req.dart';
import 'package:connectone/bai_models/already_assigned_res.dart';
import 'package:connectone/bai_models/approve_po_req.dart';
import 'package:connectone/bai_models/approve_po_res.dart';
import 'package:connectone/bai_models/area_of_business_res.dart';
import 'package:connectone/bai_models/assign_project_req.dart';
import 'package:connectone/bai_models/assign_req.dart';
import 'package:connectone/bai_models/bai_filter_res.dart';
import 'package:connectone/bai_models/bai_offline_res.dart';
import 'package:connectone/bai_models/bai_products_res.dart' as bpr;
import 'package:connectone/bai_models/cat_sub_cat_res.dart';
import 'package:connectone/bai_models/change_status_req.dart';
import 'package:connectone/bai_models/cloud_search_res.dart';
import 'package:connectone/bai_models/customer_reviews_res.dart';
import 'package:connectone/bai_models/delete_member_res.dart';
import 'package:connectone/bai_models/designation_res.dart';
import 'package:connectone/bai_models/discount_get_req.dart';
import 'package:connectone/bai_models/discount_transport_res.dart';
import 'package:connectone/bai_models/districts_res.dart';
import 'package:connectone/bai_models/edit_area_req.dart';
import 'package:connectone/bai_models/employee_list_res.dart';
import 'package:connectone/bai_models/invite_status_res.dart';
import 'package:connectone/bai_models/member_list_res.dart';
import 'package:connectone/bai_models/rating_vendor_list_res.dart';
import 'package:connectone/bai_models/report_req.dart';
import 'package:connectone/bai_models/seller_offers_res.dart';
import 'package:connectone/bai_models/edit_profile_req.dart';
import 'package:connectone/bai_models/favourite_item.dart';
import 'package:connectone/bai_models/get_projects_res.dart';
import 'package:connectone/bai_models/history_res.dart';
import 'package:connectone/bai_models/insert_stock_req.dart';
import 'package:connectone/bai_models/insert_stock_res.dart';
import 'package:connectone/bai_models/item_offering_res.dart';
import 'package:connectone/bai_models/mr_req.dart';
import 'package:connectone/bai_models/mr_res.dart';
import 'package:connectone/bai_models/nature_of_business_res.dart';
import 'package:connectone/bai_models/negotiation_req.dart';
import 'package:connectone/bai_models/offers_filter_res.dart';
import 'package:connectone/bai_models/offers_req.dart';
import 'package:connectone/bai_models/offers_res.dart';
import 'package:connectone/bai_models/org_res.dart';
import 'package:connectone/bai_models/pricing_res.dart';
import 'package:connectone/bai_models/product_item.dart';
import 'package:connectone/bai_models/product_list_res.dart';
import 'package:connectone/bai_models/purchase_order_categories_res.dart';
import 'package:connectone/bai_models/razorpay_order_req.dart';
import 'package:connectone/bai_models/razorpay_order_id_res.dart';
import 'package:connectone/bai_models/register_req.dart';
import 'package:connectone/bai_models/request_types_res.dart';
import 'package:connectone/bai_models/roles_res.dart';
import 'package:connectone/bai_models/search_categories_res.dart';
import 'package:connectone/bai_models/seller_categories_res.dart';
import 'package:connectone/bai_models/site_access_type_res.dart';
import 'package:connectone/bai_models/site_details_res.dart';
import 'package:connectone/bai_models/site_res.dart';
import 'package:connectone/bai_models/summary_res.dart';
import 'package:connectone/bai_models/team_member.dart';
import 'package:connectone/bai_models/test_search_res.dart';
import 'package:connectone/bai_models/units_res.dart';
import 'package:connectone/bai_models/upload_media_res.dart';
import 'package:connectone/bai_models/user_profile_v2_res.dart';
import 'package:connectone/bai_models/user_v2_res.dart';
import 'package:connectone/bai_models/vendor_list_res.dart';
import 'package:connectone/bai_models/vendors_res.dart';
import 'package:connectone/bai_models/view_offer_req.dart';
import 'package:connectone/bai_models/discount_tranpsortation_req.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/safe_print.dart';
import 'package:connectone/old_models/accept_bid_body.dart';
import 'package:connectone/old_models/autobid_body.dart';
import 'package:connectone/old_models/autobid_response.dart';
import 'package:connectone/old_models/bidlist_response.dart';
import 'package:connectone/old_models/cancel_autobid_body.dart';
import 'package:connectone/old_models/favourites_response.dart';
import 'package:connectone/old_models/feedback_data.dart';
import 'package:connectone/old_models/find_stock_by_id_response.dart';
import 'package:connectone/old_models/get_account_response.dart';
import 'package:connectone/old_models/highest_bid_after_close.dart';
import 'package:connectone/old_models/live_auction_list.dart';
import 'package:connectone/old_models/live_auction_response.dart';
import 'package:connectone/old_models/live_auction_stocks.dart';
import 'package:connectone/old_models/my_stocks_pojo.dart';
import 'package:connectone/old_models/notification_count_res.dart';
import 'package:connectone/old_models/notifications.dart';
import 'package:connectone/old_models/notifications_list_res.dart';
import 'package:connectone/old_models/notifs_based_on_stock.dart';
import 'package:connectone/old_models/orgaisation_model.dart';
import 'package:connectone/old_models/send_location_body.dart';
import 'package:connectone/old_models/sold_out.dart';
import 'package:connectone/old_models/status_list_model.dart';
import 'package:connectone/old_models/update_exp_price_body.dart';
import 'package:connectone/old_models/user_action_body.dart';
import 'package:connectone/old_models/withdraw_lot_body.dart';
import 'package:connectone/old_screens/login_screen.dart';
import 'package:connectone/core/utils/app_routes.dart';
import 'package:dartz/dartz.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart' as get_nav;
import 'package:get/get_core/src/get_main.dart';
import 'package:intl/intl.dart';
import 'package:mime/mime.dart';
// import 'package:platform_device_id/platform_device_id.dart';

import '../../bai_models/add_category_req.dart';
import '../../bai_models/calculator_req.dart';
import '../../bai_models/delivery_dates_res.dart';
import '../../bai_models/not_interested_req.dart';
import '../../old_models/active_buyers.dart';
import '../../old_models/buy_now_body.dart';
import '../../old_models/card_res.dart';
import '../../old_models/category_new.dart';
import '../../old_models/configuration.dart';
import '../../old_models/get_offline_stocks.dart';
import '../../old_models/get_profile_response.dart';
import '../../old_models/get_comments_summary.dart';
import '../../old_models/live_bid_body.dart';
import '../../old_models/make_offer_body.dart';
import '../../old_models/make_offline_bid_body.dart';
import '../../old_models/margin_details.dart';
import '../../old_models/margin_history.dart';
import '../../old_models/offline_filters_model.dart';
import '../../old_models/pepp_res.dart';
import '../../old_models/post_response_model.dart';
import '../../old_models/rating_summary.dart';
import '../../old_models/save_search_body.dart';
import '../../old_models/search_model.dart';
import '../../old_models/seller_info.dart';
import '../../old_models/send_token_body.dart';
import '../../old_models/sold_model.dart';
import '../../old_models/update_account_body.dart';
import '../utils/constants.dart';
import '../utils/storage_utils.dart';
import '../utils/tools.dart';
import '../utils/data_storage.dart';
import 'package:http_parser/http_parser.dart';
import 'package:path/path.dart' as p;

class NetworkController {
  static final NetworkController _singleton = NetworkController._internal();

  OrganisationData? organisationData;

  late Dio dio;

  NetworkController._internal() {
    _initializeDio();
  }

  factory NetworkController() {
    return _singleton;
  }

  void _initializeDio() {
    dio = Dio();
    dio.options.baseUrl = "$baseAppUrl/apis/";
    dio.options.connectTimeout = const Duration(seconds: 20);
    dio.options.receiveTimeout = const Duration(seconds: 20);
    dio.interceptors.add(LogInterceptor(
        responseBody: true, requestBody: true, request: true, logPrint: print));
    dio.interceptors.add(
      RetryInterceptor(
        dio: dio,
        logPrint: print,
        retries: 1,
        retryDelays: const [Duration(seconds: 5)],
        retryableExtraStatuses: {400},
      ),
    );
    dio.interceptors.add(
      QueuedInterceptorsWrapper(
        onRequest: (options, handler) {
          // ignore: prefer_interpolation_to_compose_strings
          options.headers['Authorization'] = "Bearer " + (getAuthToken() ?? "");
          try {
            options.headers['x-userpool-id'] = organisationData?.userPoolId;
          } catch (e) {
            options.headers['x-userpool-id'] = "";
            safePrint(e);
          }
          options.headers['X-NAME-NO'] = 'vtwo_dev_admin';
          safePrint(options.uri.toString());
          safePrint(jsonEncode(options.headers));
          return handler.next(options);
        },
        onResponse: (response, handler) {
          safePrint(jsonEncode(response.data));
          return handler.next(response);
        },
        onError: (DioException e, handler) {
          safePrint(e.toString());
          if (e.response!.statusCode == 401) {
            alert("Session expired, please login.");
            Get.offAll(() => const LoginScreen());
          }
          _handleError(e);
          return handler.next(e);
        },
      ),
    );
  }

  CommonResponse getError(DioException error) {
    try {
      if (error.type == DioExceptionType.connectionTimeout ||
          error.type == DioExceptionType.sendTimeout ||
          error.type == DioExceptionType.receiveTimeout ||
          error.type == DioExceptionType.connectionError ||
          error.type == DioExceptionType.badCertificate ||
          error.type == DioExceptionType.unknown ||
          error.type == DioExceptionType.badResponse) {
        return CommonResponse(status: 1, statusDescription: "Network error!");
      }
      return CommonResponse.fromJson(error.response?.data);
    } on Error {
      return CommonResponse(status: 3, statusDescription: "An error occurred!");
    }
  }

  void _handleError(DioException e) {
    String errorMessage;

    if (e.response?.statusCode == 401) {
      errorMessage = "Session expired, please login.";
      alert(errorMessage);
      Get.offAll(() => const LoginScreen());
    } else if (e.type == DioExceptionType.connectionTimeout) {
      errorMessage = "Connection timed out!";
    } else if (e.type == DioExceptionType.sendTimeout) {
      errorMessage = "Request timed out!";
    } else if (e.type == DioExceptionType.receiveTimeout) {
      errorMessage = "Response timed out!";
    } else if (e.type == DioExceptionType.connectionError) {
      errorMessage = "Network error!";
    } else if (e.type == DioExceptionType.badCertificate) {
      errorMessage = "Bad certificate!";
    } else if (e.type == DioExceptionType.unknown) {
      errorMessage = e.error?.toString() ?? "No network!";
    } else if (e.type == DioExceptionType.badResponse) {
      errorMessage =
          e.response?.data?['message']?.toString() ?? "An error occurred!";
    } else {
      errorMessage = "An error occurred!";
    }
    // alert(errorMessage);
  }

  ///*************************************************************************///
  ///                                                                         ///
  ///                               APIs                                      ///
  ///                                                                         ///
  ///*************************************************************************///

  /// get my stocks
  Future<Response<dynamic>> getStocks(String? customerId,
      {required String from, required String to}) async {
    return await dio.get(
        '$baseAppUrl/apis/bidding/my-stocks?customerId=$customerId&page=0&size=20&fromDate=$from 00:00:00&toDate=$to 23:59:59');
  }

  Future<Response<dynamic>> getStocksNextPage({
    String? customerId,
    required int page,
    required String from,
    required String to,
  }) async {
    return await dio.get(
        '$baseAppUrl/apis/bidding/my-stocks?customerId=$customerId&page=$page&size=20&fromDate=$from 00:00:00&toDate=$to 23:59:59');
  }

  /// get sold stocks
  Future<Response<dynamic>> getSoldOutStocks(String? customerId) async {
    return await dio.get(
        '$baseAppUrl/apis/bidding/stocks-by-status-cd?customerId=$customerId&status_cd=SOLD&page=0&size=3');
  }

  Future<Response<dynamic>> getSoldOutStocksNextPage({
    String? customerId,
    required int page,
  }) async {
    return await dio.get(
        '$baseAppUrl/apis/bidding/stocks-by-status-cd?customerId=$customerId&status_cd=SOLD&page=$page&size=3');
  }

  /// search my stocks by lot no
  Future<MyStocks> getStocksSearchByLotNo(
    String? customerId,
    String lotNo,
    String newDate,
  ) async {
    var url =
        "$baseAppUrl/apis/bidding/my-stocks?customerId=$customerId&lotNo=$lotNo&deliveryDate=$newDate&page=0&size=50";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return MyStocks.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// search sold stocks by lot no
  Future<SoldOut> getSoldStocksSearchByLotNo(
    String? customerId,
    String lotNo,
    String newDate,
  ) async {
    var url =
        "$baseAppUrl/apis/bidding/stocks-by-status-cd?customerId=$customerId&lotNo=$lotNo&deliveryDate=$newDate&status_cd=SOLD&page=0&size=50";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return SoldOut.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// search my stocks
  Future<MyStocks> getStocksSearchByStockId(
    String? customerId,
    String stockId,
  ) async {
    var url =
        "$baseAppUrl/apis/bidding/my-stocks?customerId=$customerId&stockId=$stockId&page=0&size=50";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return MyStocks.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// search sold stocks
  Future<SoldOut> getSoldStocksSearchByStockId(
    String? customerId,
    String stockId,
  ) async {
    var url =
        "$baseAppUrl/apis/bidding/stocks-by-status-cd?customerId=$customerId&stockId=$stockId&status_cd=SOLD&page=0&size=50";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return SoldOut.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets organisation details
  Future<Response<dynamic>> getOrganisation() async {
    try {
      var res = await dio.get("$baseAppCdnUrl/tenantmaster/details",
          queryParameters: {"url": additionalUrl});
      var data = OrganisationModel.fromJson(res.data);
      organisationData = data.data;
      return res;
    } catch (error) {
      safePrint(error.toString());
      throw Exception("Failed to fetch organisation details: $error");
    }
  }

  /// bought_mystock_table
  Future<dynamic> getBoughtTable(
    String stockId,
    String newFrom,
    String newTo,
    int page,
    int limit,
  ) async {
    var url =
        "$baseAppUrl/apis/bidding/seller-payout/buyer-with-date-filter/$stockId?page=$page&size=$limit";
    try {
      Response response = await dio.post(url,
          data: jsonEncode({
            'from_date': '$newFrom 00:00:01',
            'to_date': '$newTo 23:59:59'
          }));
      var json = response.data;
      return SoldList.fromJson(json);
    } on DioException catch (error) {
      safePrint(error);
      throw Exception(error.message);
    }
  }

  /// sold stock table
  Future<dynamic> getSoldTable(
    String stockId,
    String newFrom,
    String newTo,
    int page,
    int limit,
  ) async {
    var url =
        "$baseAppUrl/apis/bidding/seller-payout/seller-with-date-filter/$stockId?page=$page&size=$limit";
    try {
      Response response = await dio.post(url,
          data: jsonEncode({
            'from_date': '$newFrom 00:00:01',
            'to_date': '$newTo 23:59:59'
          }));
      var json = response.data;
      return SoldList.fromJson(json);
    } on DioException catch (error) {
      safePrint('$error');
      throw Exception(error.message);
    }
  }

  // get summary
  Future<dynamic> getSummary(String stockId) async {
    var url = "$baseAppUrl/apis/bidding/feedback-summary/$stockId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return RatingSummary.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error.message);
    }
  }

  /// get comments
  Future<dynamic> getComments({
    required String rVal,
    required String stockId,
  }) async {
    var url =
        "$baseAppUrl/apis/bidding/feedback-list/$stockId?page=0&size=5&rating=$rVal";

    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint('11111111111111111111111111122zz$response');
      return GetComments.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error.message);
    }
  }

  Future<dynamic> getNextComments({
    required String rVal,
    required String stockId,
    required int page,
  }) async {
    safePrint('---------------------------------$rVal');
    var url =
        "$baseAppUrl/apis/bidding/feedback-list/$stockId?page=$page&size=5&rating=$rVal";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return GetComments.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error.message);
    }
  }

  /// put review
  Future<dynamic> putreport(String reportComment, String stockId) async {
    var url = "$baseAppUrl/apis/bidding/feedback-report/$stockId";

    try {
      Response response =
          await dio.put(url, data: {'report_comment': reportComment});

      var json = response.data;

      return CommonResponse.fromJson(json);
    } catch (error) {
      safePrint('zzzzzz$error');
    }
  }

  /// set helpful
  Future<dynamic> sethelpful(String stockId, String rVal) async {
    var url = "$baseAppUrl/apis/bidding/feedback-helpful/$stockId";
    try {
      Response response = await dio.put(url);
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint('oooooooop$error');
      throw Exception(error.message);
    }
  }

  /// gets new organisation details
  Future<void> getNewOrganisation() async {
    try {
      var response = await dio.get(
        "$baseAppCdnUrl/openapis/settings/by-device-type?url=$additionalUrl&device=android",
      );
      safePrint("Config data");
      safePrint(response.data);
      var configList = List<Configuration?>.from(
          response.data!.map((x) => Configuration.fromJson(x)));
      DataStorage.configData = configList;
      safePrint("configgggg ${DataStorage.configData}");
    } catch (e) {
      safePrint(e);
    }
  }

  /// firebase custom auth
  Future<Response<dynamic>> firebaseAuth(authToken, userPoolId) async {
    return await dio.get("/bidding/user/firebase-token");
  }

  /// delete token
  Future<void> deleteToken() async {
    var url = "$baseAppUrl/apis/bidding/customers/token?url=$additionalUrl";
    var token1 = await FirebaseMessaging.instance.getToken();
    try {
      await dio.request(
        url,
        data: SendTokenBody(token: token1!).toJson(),
        options: Options(
          method: 'DELETE',
          validateStatus: (status) => true,
        ),
      );
    } on DioException catch (error) {
      safePrint(error.toString());
    }
  }

  /// gets home screen categories
  // Future<Category> getCategory(String queryUrl, String roomId) async {
  //   var url =
  //       "$baseAppUrl/openapis/product/categories?url=$queryUrl&room_id=$roomId";
  //   try {
  //     Response response = await dio.get(url);
  //     var json = response.data;
  //     return Category.fromJson(json);
  //   } on DioException catch (error) {
  //     safePrint(error.toString());
  //   }
  //   throw Exception("API call failed!");
  // }

  /// gets home screen categories new
  Future<dynamic> getCategoryNew(String queryUrl) async {
    // var url = "$baseAppUrl/openapis/category/tree?url=$queryUrl";
    var url =
        "$baseAppUrl/apis/bidding/category/tree/${getCustomerId()}?url=$queryUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return List<CategoryNew>.from(json.map((x) => CategoryNew.fromJson(x)));
    } on DioException catch (error) {
      safePrint(error.toString());
      alert(error.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Failed to fetch categories");
    }
  }

  /// gets offline stocks
  Future<OfflineStocks> getOfflineStocks({
    String product = "",
    String grade = "",
    String deliveryDate = "",
    String location = "",
    String quantity = "",
    String priceRange = "",
    String buyBidPrice = "",
    String category = "",
    int page = 0,
    String sortBy = "",
  }) async {
    var urlNew =
        "$baseAppUrl/openapis/bidding/offline-stocks?url=$additionalUrl&product=$product&deliveryDate=$deliveryDate&status_cd=PEND&page=$page&size=20&location=$location&grade=$grade&category=$category&quantity=$quantity&priceRange=$priceRange&buyBidPrice=$buyBidPrice&page=1&sort=$sortBy";
    try {
      Response response;
      // if (category != "") {
      //   response =
      //       await dio.get(urlNew, queryParameters: {"category": category});
      // } else {
      response = await dio.get(urlNew);
      // }
      var json = response.data;
      return OfflineStocks.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets offline stocks
  Future<dynamic> getOfflineStocksBaiBuyer({
    String product = "",
    String grade = "",
    String deliveryDate = "",
    String location = "",
    String quantity = "",
    String priceRange = "",
    String buyBidPrice = "",
    String category = "",
    int page = 0,
    String sortBy = "",
  }) async {
    var urlNew =
        "$baseAppUrl/apis/bidding/offline-stocks/BUYR/OPOE?url=$additionalUrl&product=$product&deliveryDate=$deliveryDate&status_cd=PEND&page=$page&size=20&location=$location&grade=$grade&category=$category&quantity=$quantity&priceRange=$priceRange&buyBidPrice=$buyBidPrice&page=1&sort=$sortBy";
    try {
      Response response;
      response = await dio.get(urlNew);
      var json = response.data;
      return BaiOfflineRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets offline stocks
  Future<dynamic> getOfflineStocksBaiSeller({
    String product = "",
    String grade = "",
    String deliveryDate = "",
    String location = "",
    String quantity = "",
    String priceRange = "",
    String buyBidPrice = "",
    String category = "",
    int page = 0,
    String sortBy = "",
  }) async {
    var urlNew =
        "$baseAppUrl/apis/bidding/offline-stocks/SELR/OPOR?url=$additionalUrl&product=$product&deliveryDate=$deliveryDate&status_cd=PEND&page=$page&size=20&location=$location&grade=$grade&category=$category&quantity=$quantity&priceRange=$priceRange&buyBidPrice=$buyBidPrice&page=1&sort=$sortBy";
    try {
      Response response;
      response = await dio.get(urlNew);
      var json = response.data;
      return BaiOfflineRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets filtered offline stocks
  Future<OfflineFilterRes> monthlyFilter(query) async {
    var url = "$baseAppUrl/apis/bidding/offline-filters";
    try {
      Response response = await dio.get(url, queryParameters: query);
      var json = response.data;
      // safePrint(json);
      return OfflineFilterRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets filtered offline stocks
  Future<BaiFilterRes> monthlyFilterBuyer(query) async {
    var url = "$baseAppUrl/apis/bidding/offline-filters/BUYR/OPOE";
    try {
      Response response = await dio.get(url, queryParameters: query);
      var json = response.data;
      // safePrint(json);
      return BaiFilterRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets filtered offline stocks
  Future<BaiFilterRes> monthlyFilterSeller(query) async {
    var url = "$baseAppUrl/apis/bidding/offline-filters/SELR/OPOR";
    try {
      Response response = await dio.get(url, queryParameters: query);
      var json = response.data;
      // safePrint(json);
      return BaiFilterRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets search filters
  Future<List<SearchModel>> searchFilters() async {
    var url = "$baseAppUrl/apis/bidding/filters";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return (json as List<dynamic>)
          .map((e) => SearchModel.fromJson(e))
          .toList();
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets profile details
  Future<Profile> getProfile() async {
    var token = getAuthToken();
    var url =
        "$baseAppUrl/apis/bidding/user/me?url=$additionalUrl&customer_id=${getCustomerId()}&auth_token=$token";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return Profile.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// get account details
  Future<Account> getAccount() async {
    var url = "$baseAppUrl/apis/bidding/user/me?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return Account.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// get favourite stocks
  Future<dynamic> getFavourites() async {
    var url = "$baseAppUrl/apis/bidding/stock_cust_info?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return FavouritesResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
      return FavouritesResponse(data: [], status: 0, statusDescription: '');
    }
  }

  /// add as favourite
  Future<dynamic> addAsFavourite({
    required String customerId,
    required String stockId,
  }) async {
    var url = "$baseAppUrl/apis/bidding/favourites";
    try {
      Response response = await dio.post(url, data: {
        "customerId": customerId,
        "stockId": stockId,
      });
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error);
    }
  }

  Future<dynamic> removeAsFavourite({
    required String customerId,
    required String stockId,
  }) async {
    var url = "$baseAppUrl/apis/bidding/favourites";
    try {
      Response response = await dio.put(url, data: {
        "customerId": customerId,
        "stockId": stockId,
      });
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error);
    }
  }

  /// edit account
  Future<CommonResponse> updateAccount({
    required String addressLine1,
    required String homeNumber,
    required String firstName,
  }) async {
    var updateAccountBody = UpdateAccountBody(
      addressLine1: addressLine1,
      firstName: firstName,
      homeNumber: homeNumber,
      url: additionalUrl,
      customerId: getCustomerId(),
    );
    var url = "$baseAppUrl/apis/bidding/user/me/update";
    try {
      Response response = await dio.post(url, data: updateAccountBody.toJson());
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error.response?.data['status_description']);
    }
  }

  /// edit account new
  Future<CommonResponse> updateAccountWebsite({
    required String website,
    String? profilePic,
  }) async {
    var url = "$baseAppUrl/apis/bidding/my-account-v2/edit";
    try {
      Response response = await dio.post(url, data: {
        "website": website,
        "title": "profile",
        "linkPrimaryKeyValue": getVendorId(),
        "linkPrimaryKeyName": "vendor_id",
        "url": profilePic,
        "previewUrl": "",
        "docType": "image"
      });
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error.response?.data['status_description']);
    }
  }

  /// cancel autobid
  Future<CommonResponse> cancelAutobid({required String stockId}) async {
    var cancelAutobidBody = CancelAutobidBody(stockId: int.parse(stockId));
    var url = "$baseAppUrl/apis/bidding/auto-bid-cancel?url=$additionalUrl";
    try {
      Response response = await dio.post(
        url,
        data: cancelAutobidBody.toJson(),
        options: Options(
          validateStatus: (status) => true,
        ),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets notifications
  Future<dynamic> getNotifications() async {
    var url = "$baseAppUrl/apis/bidding/stock-notifications?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return Notifications.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      return Notifications(data: [], status: 0, statusDescription: '');
    }
  }

  /// not in use
  Future<LiveAuctionStocks> getLiveAuctionStocks() async {
    var url =
        "$baseAppUrl/openapis/bidding/product?url=$additionalUrl&room_id=999&stockId=190863";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return LiveAuctionStocks.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets list of live auctions
  Future<LiveAuctionList> getLiveAuctionList() async {
    var url = "$baseAppUrl/apis/bidding/live_auctions";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return LiveAuctionList.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response!.statusCode.toString());
      if (error.response!.statusCode == 400) {
        Get.toNamed(AppRoutes.loginScreen);
      }
    }
    throw Exception("API call failed!");
  }

  /// search offline stocks
  Future<OfflineStocks> searchOfflineStocks({
    String lotNo = "",
    String deliveryDate = "",
    String stockId = "",
  }) async {
    // var urlNew = "$baseAppUrl/openapis/bidding/offline-stocks?url=$additionalUrl&lotNo=$lotNo&product=&deliveryDate=$deliveryDate&status_cd=PEND&page=0&size=20&location=&grade=&category=&quantity=&priceRange=&buyBidPrice=&page=1&sort=lot_no,DESC";
    var url =
        "$baseAppUrl/openapis/bidding/offline-stocks?url=$additionalUrl&lotNo=$lotNo&deliveryDate=$deliveryDate&stockId=$stockId";
    try {
      Response response = await dio.get(url);
      var json = response.data;

      return OfflineStocks.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// autobid offline
  Future<CommonResponse> autoBid({
    int quantity = 0,
    String increment = '',
    String bidDeskNo = '',
    int stockId = 0,
    String orderTypeCd = '',
    int limit = 0,
  }) async {
    var url = "$baseAppUrl/apis/bidding/auto-bid?url=$additionalUrl";
    var autobidBody = AutobidBody(
      quantity: quantity,
      increment: increment,
      customerId: getCustomerId(),
      bidDeskNo: bidDeskNo,
      stockId: stockId,
      url: additionalUrl,
      orderTypeCd: orderTypeCd,
      limit: limit,
    );
    safePrint(autobidBody.toJson().toString());
    try {
      Response response = await dio.post(
        url,
        data: autobidBody.toJson(),
        options: Options(
          validateStatus: (status) => true,
        ),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// make an offer offline
  Future<CommonResponse> makeOffer({
    int quantity = 0,
    String increment = '',
    String bidDeskNo = '',
    int stockId = 0,
    String url1 = additionalUrl,
    String orderTypeCd = '',
    int amount = 0,
  }) async {
    var url = "$baseAppUrl/apis/bidding/make-offer-bid?url=$additionalUrl";
    try {
      Response response = await dio.post(
        url,
        data: MakeOfferBody(
          quantity: quantity,
          customerId: getCustomerId(),
          bidDeskNo: bidDeskNo,
          stockId: stockId,
          url: url1,
          orderTypeCd: orderTypeCd,
          amount: amount,
        ).toJson(),
        options: Options(
          validateStatus: (status) => true,
        ),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response!.statusCode.toString());
    }
    throw Exception("API call failed!");
  }

  /// offline bid
  Future<CommonResponse> makeOffline({
    String url1 = additionalUrl,
    int quantity = 0,
    int stockId = 0,
    double amount = 0,
    int vendorId = 0,
    String bidDate = "",
    String orderTypeCd = "",
    int auctionId = 0,
    String roomId = "999",
  }) async {
    var url = "$baseAppUrl/apis/bidding/make-offer-bid?url=$additionalUrl";
    try {
      Response response = await dio.post(
        url,
        data: MakeOfflineBidBody(
          quantity: quantity,
          customerId: getCustomerId(),
          stockId: stockId,
          url: url1,
          orderTypeCd: orderTypeCd,
          amount: amount,
          vendorId: vendorId,
          roomId: roomId,
          auctionId: auctionId,
          bidDate: bidDate,
        ).toJson(),
        options: Options(
          validateStatus: (status) => true,
        ),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error.response?.data.toString());
    }
  }

  /// buy now
  Future<CommonResponse> buyNow({
    int quantity = 0,
    String increment = '',
    String bidDeskNo = '',
    int stockId = 0,
    String url1 = additionalUrl,
    String orderTypeCd = '',
    int amount = 0,
  }) async {
    var url = "$baseAppUrl/apis/bidding/buy-bid?url=$additionalUrl";
    try {
      Response response = await dio.post(
        url,
        data: BuyNowBody(
          quantity: quantity,
          customerId: getCustomerId(),
          bidDeskNo: bidDeskNo,
          stockId: stockId,
          url: url1,
          orderTypeCd: orderTypeCd,
          amount: amount,
        ).toJson(),
        options: Options(
          validateStatus: (status) => true,
        ),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// save search
  Future<void> saveSearch({
    int? buybidPriceFrom,
    int? buybidPriceTo,
    int? priceFrom,
    int? priceTo,
    int? quantityFrom,
    int? quantityTo,
    List<String>? days,
    String? location,
    String? searchName,
    String? product,
  }) async {
    var url = "$baseAppUrl/apis/bidding/filters";
    try {
      Response response = await dio.post(url,
          data: SaveSearchBody(
            buyBidPriceFrom: buybidPriceFrom,
            buyBidPriceTo: buybidPriceTo,
            customerId: int.parse(getCustomerId() ?? "0"),
            days: days ?? [],
            isWeb: "false",
            location: location ?? "",
            priceFrom: priceFrom,
            priceTo: priceTo,
            product: product ?? "",
            quantityFrom: quantityFrom,
            quantityTo: quantityTo,
            recurrence: "daily",
            searchName: searchName ?? "",
          ).toJson());
      if (response.statusCode == 201) {
        alert("Your new search has been saved successfully!");
      } else {
        alert("Unable to save your new search. Please try again.");
      }
    } on DioException catch (error) {
      safePrint(error.toString());
    }
  }

  /// delete search
  Future<void> deleteSearch({required int id}) async {
    var url = "$baseAppUrl/apis/bidding/filters/$id";
    try {
      Response response = await dio.delete(url);
      if (response.statusCode == 200) {
        alert("Deleted successfully!");
      } else {
        alert("Couldn't delete!");
      }
    } on DioException catch (error) {
      safePrint(error.toString());
    }
  }

  /// send token
  Future<void> sendToken({required String fcmToken}) async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    var url = "$baseAppUrl/apis/bidding/customers/token?url=$additionalUrl";
    try {
      safePrint("tokennnnnnn $fcmToken");
      if (fcmToken.isEmpty ||
          fcmToken == "null" ||
          fcmToken == "" ||
          fcmToken.length < 10) {
        throw Error();
      }
      String deviceId = "N/A";
      if (!kIsWeb) {
        try {
          if (Platform.isAndroid) {
            // Android
            AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
            deviceId = androidInfo.id;
          } else if (Platform.isIOS) {
            // iOS
            IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
            deviceId = iosInfo.identifierForVendor ?? "";
          }
        } catch (e) {
          safePrint('Failed to get device ID: $e');
        }
      }
      if (!kIsWeb) {
        FirebaseCrashlytics.instance.setUserIdentifier(getCustomerId() ?? "");
      }
      var osVersion = "OS_VERSION";
      var clientName = Platform.isAndroid ? "Android" : "iOS";
      var appVersion = Platform.isAndroid ? androidAppVersion : iosAppVersion;
      if (!kIsWeb) {
        if (Platform.isAndroid) {
          AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
          clientName =
              "Android - ${androidInfo.brand} - ${androidInfo.model} - ${androidInfo.device}";
          osVersion = androidInfo.version.sdkInt.toString();
        } else {
          IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
          osVersion = iosInfo.systemVersion;
        }
      }
      await dio.post(
        url,
        data: SendTokenBody(
          token: fcmToken,
          deviceId: deviceId,
          osVersion: osVersion,
          clientName: clientName,
          appVersion: appVersion,
        ).toJson(),
      );
    } catch (error) {
      safePrint(error.toString());
    }
  }

  /// send location
  Future<dynamic> sendLocation({
    required double? latitude,
    required double? longitude,
  }) async {
    var url = "$baseAppUrl/apis/bidding/user/location?url=$additionalUrl";
    try {
      Response response = await dio.post(
        url,
        data: SendLocationBody(
          latitude: latitude,
          longitude: longitude,
        ).toJson(),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } catch (error) {
      safePrint(error.toString());
    }
  }

  /// send feedback
  Future<String> postFeedback(FeedbackData data) async {
    var url = "$baseAppUrl/apis/bidding/feedback";
    try {
      safePrint(data.toJson());
      Response response = await dio.post(url, data: data.toJson());
      if (response.statusCode == 200) {
        return "Rating submitted!";
      } else {
        return "Couldn't submit rating!";
      }
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("ACouldn't submit rating!");
  }

  /// used in seller live notifications
  Future<Bidlist> getBidList(int stockId) async {
    var url = "$baseAppUrl/apis/bidding/bidlist?stockId=$stockId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return Bidlist.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// used in seller live notifications
  Future<dynamic> getFindStockById(int stockId) async {
    var url = "$baseAppUrl/apis/bidding/find-stock-by-id/$stockId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return FindStockbyId.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response!.data.toString());
      throw Exception(error.response!.data.toString());
    }
  }

  /// used in seller live notifications
  Future<NotifsBasedOnStock> getNotifsBasedOnStock(int stockId) async {
    var url =
        "$baseAppUrl/apis/bidding/stock-notifications/based-on-stock/$stockId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return NotifsBasedOnStock.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets live auction stock data
  Future<Either<CommonResponse, LiveAuction>> getLiveAuction(
      int roomId, int stockId) async {
    var url =
        "$baseAppCdnUrl/openapis/bidding/product?url=$additionalUrl&room_id=$roomId&stockId=$stockId";
    try {
      Response response = await dio.get(
        url,
        options: Options(
          validateStatus: (status) {
            return status == 200;
          },
        ),
      );
      var json = response.data;
      return Right(LiveAuction.fromJson(json));
    } on DioException catch (error) {
      safePrint(error.response.toString());
      return Left(getError(error));
    }
  }

  /// live bid
  Future<CommonResponse> liveBid({
    double quantity = 0,
    String customerId = '',
    String bidDeskNo = '',
    int stockId = 0,
    String orderTypeCd = '',
    int limit = 0,
    int vendorId = 0,
    String roomId = '',
    String bidDate = '',
    int auctionId = 0,
    double amount = 0,
  }) async {
    var url = "$baseAppUrl/apis/bidding/make-bid-v2?url=$additionalUrl";
    var livebidBody = LiveBidBody(
      quantity: quantity,
      customerId: getCustomerId(),
      bidDeskNo: 'Mobile',
      stockId: stockId,
      url: additionalUrl,
      orderTypeCd: 'BIDD',
      vendorId: vendorId,
      roomId: '999',
      bidDate: bidDate,
      amount: amount,
    );
    safePrint(livebidBody.toJson().toString());
    try {
      Response response = await dio.post(
        url,
        data: livebidBody.toJson(),
        options: Options(
          validateStatus: (status) => true,
        ),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException {
      alert("Error occurred!");
    }
    throw Exception("API call failed!");
  }

  /// live bid new
  Future<CommonResponse> livebidNew({
    double quantity = 0,
    String customerId = '',
    String bidDeskNo = '',
    int stockId = 0,
    String orderTypeCd = '',
    int limit = 0,
    int vendorId = 0,
    String roomId = '',
    String bidDate = '',
    int auctionId = 0,
    double amount = 0,
  }) async {
    var url = "$baseAppUrl/apis/bidding/make-bid-v3?url=$additionalUrl";
    var livebidBody = LiveBidBodyNew(
      quantity: quantity,
      customerId: getCustomerId(),
      bidDeskNo: 'Mobile',
      stockId: stockId,
      amount: amount,
      timeOfBid: DateTime.now().toUtc(),
    );
    safePrint(livebidBody.toJson().toString());
    try {
      Response response = await dio.post(
        url,
        data: livebidBody.toJson(),
        options: Options(
          validateStatus: (status) => true,
        ),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException {
      alert("Error occurred!");
    }
    throw Exception("API call failed!");
  }

  /// withdraw bid
  Future<CommonResponse> withdrawLot({
    int customerId = 0,
    String url = additionalUrl,
    int stockId = 0,
  }) async {
    var url = "$baseAppUrl/apis/bidding/withdraw-lot";
    var withdrawLotBody = WithdrawLotBody(
      customerId: int.parse(getCustomerId()),
      url: url,
      stockId: stockId,
    );
    try {
      Response response = await dio.post(
        url,
        data: withdrawLotBody.toJson(),
        options: Options(
          validateStatus: (status) => true,
        ),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// accept bid
  Future<CommonResponse> acceptBid({
    int bidId = 0,
    int stockId = 0,
  }) async {
    var url = "$baseAppUrl/apis/bidding/accept-bid";
    var acceptBidBody = AcceptBidBody(bidId: bidId, stockId: stockId);
    try {
      Response response = await dio.post(
        url,
        data: acceptBidBody.toJson(),
        options: Options(
          validateStatus: (status) => true,
        ),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// reject negotiation
  Future<CommonResponse> rejectNegotiation({int stockId = 0}) async {
    var url = "$baseAppUrl/apis/bidding/stock/reject-negotiation/$stockId";
    try {
      Response response = await dio.post(
        url,
        options: Options(
          validateStatus: (status) => true,
        ),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// submit negotiation
  Future<CommonResponse> submitNegotiation({
    int expPrice = 0,
    int stockId = 0,
  }) async {
    var url = "$baseAppUrl/apis/bidding/stock/update-expected-price";
    var updateExpPriceBody = UpdatedExpPriceBody(
      expectedPrice: expPrice,
      stockId: stockId,
    );
    try {
      Response response = await dio.post(
        url,
        data: updateExpPriceBody.toJson(),
        options: Options(
          validateStatus: (status) => true,
        ),
      );
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// send seller watching live signal
  Future<CommonResponse> sellerWatchingNotifs({int stockId = 0}) async {
    var url =
        "$baseAppUrl/apis/bidding/auction/seller-watching-live-ack/$stockId";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
    }
    throw Exception("API call failed!");
  }

  /// gets highest bid after auction closes
  Future<dynamic> getHighestBidAfterClose(int stockId) async {
    var url =
        "$baseAppUrl/apis/bidding/auction/highest-bid-after-close/$stockId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return HighestBidAfterClose.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
    }
  }

  /// get current margin details
  Future<dynamic> getMarginDetails() async {
    var custId = getCustomerId();
    var url = "$baseAppUrl/apis/bidding/margin/me?customerId=$custId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return MarginDetails.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
      throw Exception("Failed to get margin details");
    }
  }

  /// get margin history
  Future<dynamic> getMarginHistory(String customerId) async {
    var url = "$baseAppUrl/apis/bidding/margin/transactions/$customerId";
    safePrint(url);
    try {
      Response response = await dio.get(url,
          options: Options(validateStatus: (status) => true));
      var json = response.data;
      return MarginHistory.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
      throw Exception("Failed to get margin history");
    }
  }

  /// get pepper table
  Future<dynamic> getPepperTable(String stockId) async {
    var url =
        "$baseAppCdnUrl/openapis/specifications/pepper?url=$additionalUrl&stockId=$stockId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return PeppRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
      throw Exception("Failed to get pepper table");
    }
  }

  /// get cardamom table
  Future<dynamic> getCardamomTable(String stockId) async {
    var url =
        "$baseAppCdnUrl/openapis/specifications/cardamom?url=$additionalUrl&stockId=$stockId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return CardRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.toString());
      throw Exception("Failed to get cardamom table");
    }
  }

  Future<CommonResponse> deleteAccountApi() async {
    var url = "$baseAppUrl/apis/bidding/user/me/delete-account";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error.message);
    }
  }

  Future<CommonResponse> editExpectedPrice({
    required String expectedPrice,
    required int stockId,
  }) async {
    var url = "$baseAppUrl/apis/bidding/me/updated-expected-price-of-my-stock";
    try {
      Response response = await dio.post(url,
          data: jsonEncode({
            "expectedPrice": expectedPrice,
            "stockId": stockId,
          }));
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error.message);
    }
  }

  /// get active buyer count
  Future<dynamic> getActiveBuyers(String stockId) async {
    var url = "$baseAppUrl/apis/bidding/active-buyers/$stockId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      if (response.statusCode == 400 || response.statusCode == 401) {
        alert("Session expired, please login.");
        await NetworkController().deleteToken();
        clearAuthToken();
        writeToStorage(loggedIn, "FALSE");
        Get.offAll(() => const LoginScreen());
      }
      return ActiveBuyers.fromJson(json);
    } on DioException catch (error) {
      safePrint(error);
      throw Exception("Couldn't get active buyer count!");
    }
  }

  /// gets seller information
  Future<dynamic> getSellerinformation(String sellerId) async {
    var url = "$baseAppUrl/apis/bidding/customer/$sellerId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return SellerInfo.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception("Response failed try again later.");
    }
  }

  /// save user action
  Future<CommonResponse> saveUserAction(UserActionReq req) async {
    var url = "$baseAppUrl/apis/bidding/user-action/save";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception("Response failed try again later.");
    }
  }

  //////////////////////////////////////////////////////////////////////////////

  /// get project list
  Future<GetProjectsRes> getProjectList() async {
    var url = "$baseAppUrl/apis/bidding/project/user-project?page=0&size=50";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return GetProjectsRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get project list. Please try again later.");
    }
  }

  /// add new project
  Future<CommonResponse> addNewProject({required AddProjectReq req}) async {
    var url = "$baseAppUrl/apis/bidding/project/create";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to add new project. Please try again later.");
    }
  }

  /// cloud search
  Future<CloudSearchRes> cloudSearch({required String query}) async {
    var url =
        "https://search-baistore2-re22bfdpvovv2amsggdge6lupy.ap-southeast-1.cloudsearch.amazonaws.com/2013-01-01/search?q=$query&sort=_score+desc";
    try {
      Response response = await Dio().get(url);
      var json = response.data;
      return CloudSearchRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to complete search. Please try again later.");
    }
  }

  /// cloud search
  Future<TestSearchRes> cloudSearchTest(
      {required String query, required bool isMr}) async {
    var url =
        "$baseAppCdnUrl/openapis/itemoffering/search?url=$additionalUrl&name=$query&page=0&size=20";
    try {
      Response response = await Dio().get(url);
      var json = response.data;
      return TestSearchRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to complete search. Please try again later.");
    }
  }

  /// categories search
  Future<SearchCategoryRes> searchCategories({required String query}) async {
    var url =
        "$baseAppCdnUrl/openapis/category/search?url=$additionalUrl&name=$query&page=0&size=1000";
    try {
      Response response = await Dio().get(url);
      var json = response.data;
      return SearchCategoryRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to complete search. Please try again later.");
    }
  }

  /// get org ids
  Future<List<Org>> getOrgIds() async {
    var url = "$baseAppUrl/apis/bidding/orgs/customer-orgs/${getCustomerId()}";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      List<Org> orgList = json
          .map<Org>((data) => Org.fromJson(data as Map<String, dynamic>))
          .toList();
      safePrint(json);
      return orgList;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch org IDs. Please try again later.");
    }
  }

  /// insert stock
  Future<InsertStockRes> insertStock({required InsertStockReq req}) async {
    var url = "$baseAppUrl/apis/bidding/insert-po-stock-group";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return InsertStockRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to insert stock. Please try again later.");
    }
  }

  /// insert profile certificates and images
  Future<InsertStockRes> insertProfileImages(
      {Map<String, dynamic>? req}) async {
    var url = "$baseAppUrl/apis/bidding/user/medias";
    try {
      Response response = await dio.post(url, data: req);
      var json = response.data;
      safePrint(json);
      return InsertStockRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to insert stock. Please try again later.");
    }
  }

  /// get units
  Future<UnitsRes> getUnits() async {
    var url = "$baseAppUrl/apis/bidding/units?url=$additionalUrl&room_id=999";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return UnitsRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch units. Please try again later.");
    }
  }

  /// get site access
  Future<SiteAccessTypeList> getSiteAccess() async {
    var url =
        "$baseAppUrl/openapis/dropdown-options/road-access-types?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return SiteAccessTypeList.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch site access. Please try again later.");
    }
  }

  Future<UploadMediaRes> uploadMedia(String path) async {
    var url = "$baseAppUrl/apis/bidding/bid-image-upload?file";
    FormData formData = FormData();
    try {
      String mimeType = getMimeType(path);
      if (!isSupportedMimeType(mimeType)) {
        throw Exception("Unsupported file type: $mimeType");
      }

      formData.files.add(
        MapEntry(
          'file',
          await MultipartFile.fromFile(
            path,
            filename: generateRandomFilename(p.extension(path).substring(1)),
            contentType:
                MediaType(mimeType.split('/')[0], mimeType.split('/')[1]),
          ),
        ),
      );

      safePrint(formData.files.first.value.contentType);

      dio.options.headers["Content-Type"] = "multipart/form-data";

      Response response = await dio.post(url, data: formData);
      dio.options.headers["Content-Type"] = "application/json";
      var json = response.data;
      safePrint(json);
      return UploadMediaRes.fromJson(json);
    } on DioException catch (error) {
      dio.options.headers["Content-Type"] = "application/json";
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to upload media. Please try again later.");
    }
  }

  Future<UploadMediaRes> uploadMediaWeb(Uint8List bytes) async {
    var url = "$baseAppUrl/apis/bidding/bid-image-upload?file";
    FormData formData = FormData();
    try {
      // String mimeType = getMimeType(path);
      // if (!isSupportedMimeType(mimeType)) {
      //   throw Exception("Unsupported file type: $mimeType");
      // }
      var mimeType = lookupMimeType('', headerBytes: bytes) ?? "*/*";
      formData.files.add(
        MapEntry(
          'file',
          MultipartFile.fromBytes(
            bytes,
            filename: generateRandomFilename(mimeType.split('/')[1]),
            contentType:
                MediaType(mimeType.split('/')[0], mimeType.split('/')[1]),
          ),
        ),
      );

      safePrint(formData.files.first.value.contentType);

      dio.options.headers["Content-Type"] = "multipart/form-data";

      Response response = await dio.post(url, data: formData);
      dio.options.headers["Content-Type"] = "application/json";
      var json = response.data;
      safePrint(json);
      return UploadMediaRes.fromJson(json);
    } on DioException catch (error) {
      dio.options.headers["Content-Type"] = "application/json";
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to upload media. Please try again later.");
    }
  }

  /// get site access
  // Future<DistrictsRes> getCentres() async {
  //   var url = "$baseAppUrl/openapis/dropdown-options/centers";
  //   try {
  //     Response response = await dio.get(url);
  //     var json = response.data;
  //     safePrint(json);
  //     return DistrictsRes.fromJson(json);
  //   } on DioException catch (error) {
  //     safePrint(error.response.toString());
  //     throw Exception("Unable to fetch districts. Please try again later.");
  //   }
  // }

  Future<DistrictsRes> getBaiCentres() async {
    var url =
        "$baseAppUrl/openapis/dropdown-options/bai-centers?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return DistrictsRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch districts. Please try again later.");
    }
  }

  Future<DistrictsRes> getDistricts() async {
    var url =
        "$baseAppUrl/openapis/dropdown-options/districts?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return DistrictsRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch districts. Please try again later.");
    }
  }

  /// get site access
  Future<DesignationsRes> getDesignations() async {
    var url =
        "$baseAppUrl/openapis/dropdown-options/designations?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return DesignationsRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch designations. Please try again later.");
    }
  }

  /// get site access
  Future<VendorsRes> getVendors(String id) async {
    var url =
        "$baseAppUrl/openapis/dropdown-options/vendors/$id?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return VendorsRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch vendors. Please try again later.");
    }
  }

  getVendors1(String id, String query, String vendorType) async {
    var url =
        "$baseAppUrl/openapis/dropdown-options/vendors/$vendorType/$id?url=$additionalUrl&$query";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return VendorsRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch vendors. Please try again later.");
    }
  }

  /// get site access
  Future<NatureOfBusinessRes> getNatureOfBusinessBuyer() async {
    var url =
        "$baseAppUrl/openapis/dropdown-options/nature-of-business-buyer?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return NatureOfBusinessRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch nature of businesses. Please try again later.");
    }
  }

  /// get site access
  Future<NatureOfBusinessRes> getNatureOfBusinessSeller() async {
    var url =
        "$baseAppUrl/openapis/dropdown-options/nature-of-business-seller?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return NatureOfBusinessRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch nature of businesses. Please try again later.");
    }
  }

  /// get site access
  Future<AreaOfBusinessRes> getareaOfBusiness() async {
    var url =
        "$baseAppUrl/openapis/dropdown-options/area-of-business?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return AreaOfBusinessRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch area of businesses. Please try again later.");
    }
  }

  /// get site access
  Future<PurchaseOrderCategoriesRes> getPurchaseOrderCategories() async {
    var url =
        "$baseAppUrl/openapis/dropdown-options/purchase-order-categories?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return PurchaseOrderCategoriesRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch purchase order categories. Please try again later.");
    }
  }

  Future<List<RolesRes>> getRoles(String id, String type) async {
    var url =
        "$baseAppUrl/openapis/bai/registration/user-role/$type/$id?url=$additionalUrl";

    try {
      Response response = await dio.get(url);

      var json = response.data;
      safePrint(json);
      List<RolesRes> result =
          List<RolesRes>.from(json.map((x) => RolesRes.fromJson(x)));
      return result;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch roles. Please try again later.");
    }
  }

  /// get site access
  Future<ItemOfferingRes> getItemOffering(String? itemId) async {
    var url = "$baseAppUrl/openapis/itemoffering/$itemId?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return ItemOfferingRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch item offering. Please try again later.");
    }
  }

  Future<BaiFilterRes> getBaiFilters(String queryString, String code) async {
    // var url = "$baseAppUrl/apis/bidding/filter-list/$code?$queryString";
    // var url = "$baseAppUrl/apis/bidding/mvt-prch-ordr/filters?type=$code&$queryString";
    var url = "$baseAppUrl/apis/bidding/my-requests/filters-v2?$queryString";
    try {
      Response response = await dio.get(Uri.encodeFull(url));
      var json = response.data;
      safePrint(json);
      return BaiFilterRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch filters. Please try again later.");
    }
  }

  Future<bpr.BaiProductsRes> getBaiProductList({
    String queryString = "",
    int page = 0,
    String code = "",
    String sortBy = "",
  }) async {
    // var url = "$baseAppUrl/apis/bidding/filter/stock-list/$code?page=$page&size=25&$queryString";
    // var url = "$baseAppUrl/apis/bidding/mvt-prch-ordr?type=$code&page=$page&size=25&sort=$sortBy&$queryString";
    var url =
        "$baseAppUrl/apis/bidding/my-requests/paginated?page=$page&size=25&sort=$sortBy&$queryString";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return bpr.BaiProductsRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch product list. Please try again later.");
    }
  }

  Future<BaiFilterRes> getPoFilters(String queryString, String code) async {
    // var url = "$baseAppUrl/apis/bidding/filter-list/$code?$queryString";
    var url =
        "$baseAppUrl/apis/bidding/prch-order-confirmed/filters?type=$code&$queryString";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return BaiFilterRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch filters. Please try again later.");
    }
  }

  Future<bpr.BaiProductsRes> getPoProductList({
    String queryString = "",
    int page = 0,
    String code = "",
    String sortBy = "",
  }) async {
    // var url = "$baseAppUrl/apis/bidding/filter/stock-list/$code?page=$page&size=25&$queryString";
    var url =
        "$baseAppUrl/apis/bidding/prch-order-confirmed?type=$code&page=$page&size=25&sort=$sortBy&$queryString";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return bpr.BaiProductsRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch product list. Please try again later.");
    }
  }

  Future<List<PricingRes>> getPricing() async {
    var url =
        "$baseAppUrl/openapis/bai/registration/categories-and-price?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      List<PricingRes> result =
          List<PricingRes>.from(json.map((x) => PricingRes.fromJson(x)));
      return result;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch pricing list. Please try again later.");
    }
  }

  Future<CommonResponse> registerUser(RegisterReq req) async {
    var url =
        "$baseAppUrl/openapis/bai/registration/signup-cognito?url=$additionalUrl";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to register user. Please try again later.");
    }
  }

  Future<VendorList> getVendorList({
    required String name,
    required String district,
    required String projectId,
  }) async {
    var url =
        "$baseAppUrl/apis/bidding/assign-customers/vendor-list/$projectId?name=$name&district=$district";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      // List<AssignVendor> result =
      //     List<AssignVendor>.from(json.map((x) => AssignVendor.fromJson(x)));
      return VendorList.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get vendors. Please try again later.");
    }
  }

  Future<VendorList> getVendorListV2({
    required String name,
    required String district,
    required String prchOrdrSplitId,
    required DateTime deliveryDate,
  }) async {
    String formatted = DateFormat('yyyy-MM-dd HH:mm:ss').format(deliveryDate);
    var url =
        "$baseAppUrl/apis/bidding/assign-customers/vendor-list-v2?name=$name&district=$district&prchOrdrSplitId=$prchOrdrSplitId&deliveryDate=$formatted";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      // List<AssignVendor> result =
      //     List<AssignVendor>.from(json.map((x) => AssignVendor.fromJson(x)));
      return VendorList.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get vendors. Please try again later.");
    }
  }

  Future<List<AlreadyAssignedRes>> getAlreadyAssigned(
      {required String id}) async {
    var url = "$baseAppUrl/apis/bidding/assign-customers/assignments-list/$id";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      List<AlreadyAssignedRes> result = List<AlreadyAssignedRes>.from(
          json.map((x) => AlreadyAssignedRes.fromJson(x)));
      return result;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get already assigned list. Please try again later.");
    }
  }

  Future<UserV2Res> getUserV2() async {
    var url = "$baseAppUrl/apis/bidding/user/me-v2";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return UserV2Res.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get user details. Please try again later.");
    }
  }

  Future<UserProfileV2Res> getUserProfileV2(num? vendorID) async {
    var url = "$baseAppUrl/apis/bidding/my-account-v2/$vendorID";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return UserProfileV2Res.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get user details. Please try again later.");
    }
  }

  // Future<UserProfileV2Res> getUserProfileV2() async {
  //   var url = "$baseAppUrl/apis/bidding/my-account-v2";
  //   try {
  //     Response response = await dio.get(url);
  //     var json = response.data;
  //     safePrint(json);
  //     return UserProfileV2Res.fromJson(json);
  //   } on DioException catch (error) {
  //     safePrint(error.response.toString());
  //     throw Exception(error.response?.data?["status_description"] ?? "Unable to get user details. Please try again later.");
  //   }
  // }

  Future<SellerCategoresRes> getSellerCategories() async {
    var url = "$baseAppUrl/apis/bidding/category/seller-categories";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return SellerCategoresRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get user details. Please try again later.");
    }
  }

  Future<List<HistoryRes>> getHistory(String? prchOrdrId) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr-history/$prchOrdrId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      List<HistoryRes> result =
          List<HistoryRes>.from(json.map((x) => HistoryRes.fromJson(x)));
      return result;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get history details. Please try again later.");
    }
  }

  Future<CustomerReviewsRes> getUserReviews(String? id) async {
    var url = "$baseAppUrl/apis/bidding/feedback-list/$id";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return CustomerReviewsRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get user details. Please try again later.");
    }
  }

  Future<RazorpayOrderIdRes> getRazorPayOrderId(
      CreateRazorpayOrderReq req) async {
    var url = "$baseAppUrl/apis/bidding/payment/create-razorpay-order-id";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return RazorpayOrderIdRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get RazorPay Order ID. Please try again later.");
    }
  }

  Future<List<CatSubCatRes>> getCatSubCat(bool isMr) async {
    var url =
        "$baseAppUrl/apis/bidding/category-subcategory/${isMr ? "mr-list" : "sr-list"}";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      var res =
          List<CatSubCatRes>.from(json.map((x) => CatSubCatRes.fromJson(x)));
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get category - sub category. Please try again later.");
    }
  }

  Future<List<ProductItem>> getProductBySubCat(String id) async {
    var url =
        "$baseAppUrl/apis/bidding/category-subcategory/product-by-subcategory/$id?page=0&size=100";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      var res =
          List<ProductItem>.from(json.map((x) => ProductItem.fromJson(x)));
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get products by sub category. Please try again later.");
    }
  }

  Future<ProductListRes> assignVendor(AssignReq req) async {
    var url = "$baseAppUrl/apis/bidding/assign-customers/insert";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return ProductListRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to assign vendor. Please try again later.");
    }
  }

  Future<CommonResponse> changeStatus(String stockId) async {
    // var url = "$baseAppUrl/apis/bidding/stock/change-status/$stockId";
    var url = "$baseAppUrl/apis/bidding/post-purchase-order/$stockId";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to change status. Please try again later.");
    }
  }

  Future<ProductListRes> getAssignments(String stockId) async {
    var url =
        "$baseAppUrl/apis/bidding/assign-customers/assignments-list/$stockId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return ProductListRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get assignments. Please try again later.");
    }
  }

  Future<BuyerOffersRes> getBuyerOffers(
    String prchId,
    ViewOfferReq req, {
    String? brandName = "",
  }) async {
    brandName ?? (brandName = "");
    var url =
        "$baseAppUrl/apis/bidding/prch-ordr-offers/list/$prchId?brandName=$brandName&deliveryDate=";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return BuyerOffersRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get offers. Please try again later.");
    }
  }

  Future<BuyerOffersRes> getBuyerOffersWithOfferId(
    int offerId,
    ViewOfferReq req, {
    String? brandName = "",
  }) async {
    brandName ?? (brandName = "");
    var url = "$baseAppUrl/apis/bidding/prch-ordr-offers/list/v2/$offerId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return BuyerOffersRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get offers. Please try again later.");
    }
  }

  Future<SellerOffers> getOffersSeller(String prchId, ViewOfferReq req) async {
    var url =
        "$baseAppUrl/apis/bidding/prch-ordr-variants/list-by-variant-offers/$prchId";
    try {
      Response response = await dio.post(
        url,
        data: req.toJson(),
      );
      var json = response.data;
      safePrint(json);
      return SellerOffers.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get offers. Please try again later.");
    }
  }

  Future<List<TeamMember>> getTeamMembers() async {
    var url = "$baseAppUrl/apis/bidding/customer-approval/list";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      var res = List<TeamMember>.from(json.map((x) => TeamMember.fromJson(x)));
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get team members. Please try again later.");
    }
  }

  Future<dynamic> approveTeamMembers(String id) async {
    var url = "$baseAppUrl/apis/bidding/customer-approval/approve/$id";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      safePrint(json);
      return TeamMember.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to approve team member. Please try again later.");
    }
  }

  Future<dynamic> rejectTeamMembers(String id) async {
    var url = "$baseAppUrl/apis/bidding/customer-approval/reject/$id";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      safePrint(json);
      return TeamMember.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to reject team member. Please try again later.");
    }
  }

  Future<PostResponse> postOffers(OffersReq req) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr-offers/create";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return PostResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to post offers. Please try again later.");
    }
  }

  Future<ApprovePoRes> approvePo(ApprovePoReq req) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr-variants/approve-variant";
    try {
      Response response = await dio.post(
        url,
        options: Options(validateStatus: (status) => true),
        data: req.toJson(),
      );
      var json = response.data;
      safePrint(json);
      return ApprovePoRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to approve purchase order. Please try again later.");
    }
  }

  Future<CommonResponse> rejectPo(String id) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr-variants/reject-variant/$id";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to reject purchase order. Please try again later.");
    }
  }

  Future<CommonResponse> postNegotiation(String id, NegotiationReq req) async {
    var url =
        "$baseAppUrl/apis/bidding/prch-ordr-offers/request-negotiation/$id";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to post negotiation. Please try again later.");
    }
  }

  Future<OffersFilterRes> getOffersFilter(String id) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr-offers/filters/$id";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return OffersFilterRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get filters. Please try again later.");
    }
  }

  // Future<List<FavouriteItem>> getNewFavourites() async {
  //   var url = "$baseAppUrl/apis/bidding/prch-ordr/favourites";
  //   try {
  //     Response response = await dio.get(url);
  //     var json = response.data;
  //     safePrint(json);
  //     var res =
  //         List<FavouriteItem>.from(json.map((x) => FavouriteItem.fromJson(x)));
  //     return res;
  //   } on DioException catch (error) {
  //     safePrint(error.response.toString());
  //     return [];
  //     // throw Exception(error.response?.data?["status_description"] ?? "Unable to get favourites. Please try again later.");
  //   }
  // }

  Future<List<FavouriteItem>> getNewFavourites() async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr/favourites";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint("Response JSON: $json"); // Print for debugging

      if (json != null && json is List) {
        // Check for null AND List type
        if (json.isEmpty || (json.length == 1 && json[0] == null)) {
          // Handle [], [null] cases
          safePrint("No favourites found (empty or [null] response)");
          return []; // Return empty list if no favourites
        } else {
          try {
            var res = List<FavouriteItem>.from((json)
                .map((x) => FavouriteItem.fromJson(x as Map<String, dynamic>)));
            return res;
          } catch (e) {
            safePrint("Error during FavouriteItem.fromJson: $e");
            return []; // Handle fromJson error
          }
        }
      } else {
        safePrint(
            "Unexpected response format (not a List or null response data)");
        return []; // Handle unexpected format
      }
    } on DioException catch (error) {
      safePrint("DioError: ${error.response.toString()}");
      return []; // Handle DioError
    }
  }

  Future<MrRes> getMrData(String id) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr/$id";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return MrRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get Material Request data. Please try again later.");
    }
  }

  Future<CommonResponse1> editMrData(String id, MrReq req) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr/$id";
    try {
      Response response = await dio.put(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return CommonResponse1.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["statusDescription"] ??
          "Unable to edit Material Request data. Please try again later.");
    }
  }

  Future<dynamic> getRequestTypes() async {
    var url =
        "$baseAppUrl/openapis/prch-ordr/active-current?url=$additionalUrl";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      var res =
          List<RequestTypes>.from(json.map((x) => RequestTypes.fromJson(x)));
      DataStorage.requestTypes = res;
      return res;
    } on DioException catch (error) {
      alert("message");
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get request types. Please try again later.");
    }
  }

  Future<dynamic> getSiteList() async {
    var url = "$baseAppUrl/apis/bidding/project/user-project?page=0&size=50";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return SiteRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get team members. Please try again later.");
    }
  }

  Future<CommonResponse> assignProjectToUser(AssignProjectReq req) async {
    var url = "$baseAppUrl/apis/bidding/site/assign";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to assign project to user. Please try again later.");
    }
  }

  Future<CommonResponse> updateProjectStatus(int pid, String status) async {
    var url = "$baseAppUrl/apis/bidding/site/update-status/$pid?status=$status";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to update project status. Please try again later.");
    }
  }

  Future<dynamic> getCustomersList() async {
    var url = "$baseAppUrl/apis/bidding/customer-list";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return CustomersListRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get team members. Please try again later.");
    }
  }

  Future<dynamic> markAsRead(int id) async {
    var url = "$baseAppUrl/apis/bidding/mvt-prch-ordr/mark-read/$id";
    try {
      Response response = await dio.put(url);
      var json = response.data;
      safePrint(json);

      return CommonResponse(
          status: response.statusCode ?? 200,
          statusDescription: response.statusMessage ?? "");
    } on DioException catch (error) {
      alert("message");
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get request types. Please try again later.");
    }
  }

  Future<bpr.BaiProductsRes> getBaiProductListforNotification({
    String queryString = "",
    int page = 0,
    String code = "",
    String sortBy = "",
  }) async {
    var url =
        "$baseAppUrl/apis/bidding/my-requests/paginated?page=0&size=&sort=&prchOrdrId=$queryString";
    try {
      Response response = await dio.get(url,
          options: Options(validateStatus: (status) => true));
      var json = response.data;
      safePrint(json);
      return bpr.BaiProductsRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch product. Please try again later.");
    }
  }

  Future<bpr.Content> getBaiProductHistory(
      {required String mvtPrchOrdrHistoryId}) async {
    var url =
        "$baseAppUrl/apis/bidding/prch-ordr/history/$mvtPrchOrdrHistoryId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return bpr.Content.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch product. Please try again later.");
    }
  }

  Future<CommonResponse> updateAccountDetails({
    EditProfileReq? req,
  }) async {
    var url = "$baseAppUrl/apis/bidding/my-account-v2/edit";
    try {
      Response response = await dio.post(url, data: req?.toJson());
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error.response?.data['status_description'] ??
          "Unable to update account. Please try again later.");
    }
  }

  Future<StatusListModel> getStatusDropdown(
    String statusCd, {
    String prchOrdrId = '',
    String? categoryId = '',
    int? prchOrdrSplitId,
    DateTime? deliveryDate,
  }) async {
    var date = deliveryDate != null
        ? "${deliveryDate.toIso8601String().split('T')[0]} 00:00:00"
        : '';
    // var url =
    //     "$baseAppUrl/apis/bidding/next-status/$statusCd?prchOrdrId=$prchOrdrId&categoryId=$categoryId&deliveryDate=$date&prchOrdrSplitId=$prchOrdrSplitId";
    var url =
        "$baseAppUrl/apis/bidding/next-status/$statusCd?prchOrdrId=$prchOrdrId&deliveryDate=$date&prchOrdrSplitId=${categoryId?.toEmptyIfZero()}";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return StatusListModel.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch next status. Please try again later.");
    }
  }

  Future<CommonResponse> changeStatusOfMR({
    ChangeStatusReq? req,
  }) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr-history/create";
    try {
      Response response = await dio.post(url, data: req!.toJson());
      var json = response.data;
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      throw Exception(error.response?.data['status_description'] ??
          "Unable to change status. Please try again later.");
    }
  }

  Future<dynamic> getNotificationsnew() async {
    var url = "$baseAppUrl/apis/bidding/mr-notifications";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      return NotificationsListRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      return NotificationsListRes(data: [], status: 0, statusDescription: '');
    }
  }

  Future<dynamic> readNotification(int notificationId) async {
    var url = "$baseAppUrl/apis/bidding/mr-notifications/read/$notificationId";
    try {
      Response response = await dio.put(url);
      var json = response.data;
      return CommonResponse.fromJson(json);
    } catch (error) {
      safePrint('$error');
    }
  }

  Future<NotificationsCountRes> getNotificationCount() async {
    var url = "$baseAppUrl/apis/bidding/mr-notifications/unread-count";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      return NotificationsCountRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch next notification count. Please try again later.");
    }
  }

  Future<CommonResponse> acceptNegotiation(
      String id, AcceptNegotiationReq req) async {
    var url =
        "$baseAppUrl/apis/bidding/prch-ordr-offers/accept-negotiation/$id";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to accept negotiation. Please try again later.");
    }
  }

  Future<InviteStatusRes> getInviteStatusList(String prchOrdrId) async {
    var url =
        "$baseAppUrl/apis/bidding/prch-ordr-offers/invite-status/$prchOrdrId";
    try {
      Response response = await dio.get(url,
          options: Options(validateStatus: (status) => true));
      var json = response.data;
      safePrint(json);
      return InviteStatusRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch quote status list. Please try again later.");
    }
  }

  Future<List<bpr.Content>> getMrGrouping(String ordrGrpId) async {
    var url =
        "$baseAppUrl/apis/bidding/prch-ordr/grouped-mr?order_group_id=$ordrGrpId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      var res =
          List<bpr.Content>.from(json.map((x) => bpr.Content.fromJson(x)));
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch MR grouping. Please try again later.");
    }
  }

  Future<CommonResponse> postdiscountTransportation(DiscountReq req) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr-offr-grp/create";
    try {
      Response response = await dio.post(url, data: req.toJson());
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to post. Please try again later.");
    }
  }

  Future<List<SummaryResponse>> getQuoteSummary(
    int id, {
    String? vendorId,
    String responseType = "QTSM",
    bool enablePoSummary = false,
    required DateTime date,
    required String categoryId,
    int? prchOrdrSplitId,
  }) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr-offers/offer-summary";
    var categoryIdNew = int.tryParse(categoryId) ?? 0;
    var param = {
      "vendorName": null,
      "vendorId": vendorId,
      "orderGroupId": id,
      "responseType": responseType,
      // "categoryId": categoryIdNew,
      "prchOrdrSplitId": prchOrdrSplitId,
      "enablePoSummary": enablePoSummary,
      "deliveryDate": date.toIso8601String(),
      "products": [
        {"brand": null, "quantity": null, "pricePerUnit": null}
      ]
    };

    try {
      Response response = await dio.post(url, data: param);
      var json = response.data;
      var res = List<SummaryResponse>.from(
          json.map((x) => SummaryResponse.fromJson(x)));
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get offers. Please try again later.");
    }
  }

  Future<List<SummaryResponse>> getQuoteSummarySteel(
    int id, {
    String? vendorId,
    String responseType = "QTSM",
    required DateTime date,
    required String categoryId,
    int? prchOrdrSplitId,
  }) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr-offers/offer-summary-steel";
    var categoryIdNew = int.tryParse(categoryId) ?? 0;
    var param = {
      "vendorName": null,
      "vendorId": vendorId,
      "orderGroupId": id,
      "responseType": responseType,
      // "categoryId": categoryIdNew,
      "prchOrdrSplitId": prchOrdrSplitId,
      "deliveryDate": date.toIso8601String(),
      "products": [
        {"brand": null, "quantity": null, "pricePerUnit": null}
      ]
    };

    try {
      Response response = await dio.post(url, data: param);
      var json = response.data;
      var res = List<SummaryResponse>.from(
          json.map((x) => SummaryResponse.fromJson(x)));
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get offers. Please try again later.");
    }
  }

  // Future<DiscountTransportRes> getdiscountTransportation(
  //     DiscountGetReq discountGetReq) async {
  //   var url = "$baseAppUrl/apis/bidding/prch-ordr-offr-grp/get";
  //   try {
  //     Response response = await dio.post(url, data: discountGetReq.toJson());
  //     var json = response.data;
  //     safePrint(json);
  //     var res = DiscountTransportRes.fromJson(json);
  //     return res;
  //   } on DioException catch (error) {
  //     safePrint(error.response.toString());
  //     throw Exception(error.response?.data?["status_description"] ??
  //         "Unable to fetch discount and transportation charges. Please try again later.");
  //   }
  // }

  Future<DiscountTransportRes> getdiscountTransportation(
      DiscountGetReq discountGetReq) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr-offr-grp/get-v2";
    try {
      Response response = await dio.post(url, data: discountGetReq.toJson());
      var json = response.data;
      safePrint(json);
      var res = DiscountTransportRes.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to fetch discount and transportation charges. Please try again later.");
    }
  }

  Future<CommonResponse> postGst(
    String productId,
    String gst,
  ) async {
    var url =
        "$baseAppUrl/apis/bidding/product-gst/create?productId=$productId&gst=$gst";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      safePrint(json);
      var res = CommonResponse.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to add GST. Please try again later.");
    }
  }

  Future<CommonResponse> sendEmail(
    String ordrGrpId,
    DateTime date,
  ) async {
    var url =
        "$baseAppUrl/apis/bidding/prch-ordr-offers/summary-email/$ordrGrpId?deliveryDate=${date.toIso8601String()}";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      safePrint(json);
      var res = CommonResponse.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to send email. Please try again later.");
    }
  }

  Future<CommonResponse> discardOffer({
    required String prchOrdrOffrId,
    required bool isDiscard,
  }) async {
    var url =
        "$baseAppUrl/apis/bidding/prch-ordr-offers/discard/$prchOrdrOffrId?isDiscard=$isDiscard";
    try {
      Response response = await dio.put(url);
      var json = response.data;
      safePrint(json);
      var res = CommonResponse.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to discard. Please try again later.");
    }
  }

  // Report problem
  Future<CommonResponse> reportProblem(ReportReq req) async {
    var url = "$baseAppUrl/apis/bidding/report-problem";
    try {
      Response response = await dio.post(
        url,
        data: req.toJson(),
      );
      var json = response.data;
      safePrint(json);
      var res = CommonResponse.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to report problem. Please try again later.");
    }
  }

  Future<CommonResponse> remindInvite(
      {required int prchOrdrId, required int vendorId}) async {
    var url = "$baseAppUrl/apis/bidding/remind-vendor";
    try {
      Response response = await dio.post(
        url,
        data: {
          "prchOrdrId": prchOrdrId,
          "vendorId": vendorId,
        },
      );
      var json = response.data;
      safePrint(json);
      var res = CommonResponse.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to remind vendor. Please try again later.");
    }
  }

  Future<RatingVendorListRes> ratingVendorList(String prchOrdrId) async {
    var url = "$baseAppUrl/apis/bidding/vendor-list/$prchOrdrId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      var res = RatingVendorListRes.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get vendor list. Please try again later.");
    }
  }

  Future<SiteDetailsRes> siteDetails(String projectId) async {
    var url = "$baseAppUrl/apis/bidding/project/$projectId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      var res = SiteDetailsRes.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get site details. Please try again later.");
    }
  }

  Future<CommonResponse> addEmployee(AddEmployeeReq req) async {
    var url = "$baseAppUrl/apis/bidding/project-employee/assign";
    try {
      Response response = await dio.post(
        url,
        data: req.toJson(),
      );
      var json = response.data;
      safePrint(json);
      var res = CommonResponse.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to add employee. Please try again later.");
    }
  }

  Future<CommonResponse> changeEmployeeStatus(
    String projectId,
    String employeeId,
    String status,
  ) async {
    var url =
        "$baseAppUrl/apis/bidding/project-employee/change-status?projectId=$projectId&employeeId=$employeeId&status=$status";
    try {
      Response response = await dio.put(url);
      var json = response.data;
      safePrint(json);
      var res = CommonResponse.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to change employee status. Please try again later.");
    }
  }

  // change project status
  Future<CommonResponse> changeProjectStatus(
    String projectId,
    String status,
  ) async {
    var url =
        "$baseAppUrl/apis/bidding/project/update-status/$projectId?status=$status";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      safePrint(json);
      var res = CommonResponse.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to change project status. Please try again later.");
    }
  }

  Future<EmployeeListRes> getEmployeeList(String projectId) async {
    var url =
        "$baseAppUrl/apis/bidding/project-employee/list?projectId=$projectId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      var res = EmployeeListRes.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get employee list. Please try again later.");
    }
  }

  Future<List<MemberListItem>> getMemberList() async {
    var url = "$baseAppUrl/apis/bidding/team-members/list";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      var res = List<MemberListItem>.from(
          json.map((x) => MemberListItem.fromJson(x)));
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get member list. Please try again later.");
    }
  }

  Future<DeleteMemberRes> deleteMember(String customerId) async {
    var url = "$baseAppUrl/apis/bidding/user/delete-user/$customerId";
    try {
      Response response = await dio.delete(url);
      var json = response.data;
      safePrint(json);
      return DeleteMemberRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to delete member. Please try again later.");
    }
  }

  Future<CommonResponse> assignToNewMember(
    String deletedCustomerId,
    String newCustomerId,
  ) async {
    var url =
        "$baseAppUrl/apis/bidding/assign-customers/assign-customer-data?deleted_customer_id=$deletedCustomerId&new_customer_id=$newCustomerId";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to assign member. Please try again later.");
    }
  }

  Future<CommonResponse> addSellerOrParticipant(AddSellerReq req) async {
    var url =
        "$baseAppUrl/openapis/bid-register-signup-cognito-v2?url=$additionalUrl";
    try {
      Response response = await dio.post(
        url,
        data: req.toJson(),
      );
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to add seller. Please try again later.");
    }
  }

  // Future<CalculatorRes> calculatePrice(CalculatorReq req) async {
  //   var url = "$baseAppUrl/apis/bidding/gst-calculation";
  //   try {
  //     Response response = await dio.post(
  //       url,
  //       data: req.toJson(),
  //     );
  //     var json = response.data;
  //     safePrint(json);
  //     return CalculatorRes.fromJson(json);
  //   } on DioException catch (error) {
  //     safePrint(error.response.toString());
  //     throw Exception(error.response?.data?["status_description"] ??
  //         "Unable to calculate price. Please try again later.");
  //   }
  // }

  Future<CalculatorRes> calculatePrice(CalculatorReq req) async {
    var url = "$baseAppUrl/apis/bidding/gst-calculation-v2";
    try {
      Response response = await dio.post(
        url,
        data: req.toJson(),
      );
      var json = response.data;
      safePrint(json);
      return CalculatorRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to calculate price. Please try again later.");
    }
  }

  Future<AllowEditRes> allowEdit({
    required String deliveryDate,
    required String orderGroupId,
  }) async {
    var url =
        "$baseAppUrl/apis/bidding/check-delivery-date?deliveryDate=$deliveryDate&orderGroupId=$orderGroupId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      var res = AllowEditRes.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get allow edit. Please try again later.");
    }
  }

  Future<CommonResponse> notInterested(NotInterestedReq req) async {
    var url = "$baseAppUrl/apis/bidding/mark-as-notinterested";
    try {
      Response response = await dio.post(
        url,
        data: req.toJson(),
      );
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to mark as not interested. Please try again later.");
    }
  }

  Future<CommonResponse> addCategory(AddCategoryReq req) async {
    var url = "$baseAppUrl/apis/bidding/register-new-category";
    try {
      Response response = await dio.post(
        url,
        data: req.toJson(),
      );
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to register new category. Please try again later.");
    }
  }

  Future<CommonResponse> editArea(EditAreaReq req) async {
    var url = "$baseAppUrl/apis/bidding/area-of-business/edit";
    try {
      Response response = await dio.post(
        url,
        data: req.toJson(),
      );
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to edit area. Please try again later.");
    }
  }

  Future<DeliveryDatesRes> getDeliveryDates({
    required String orderGroupId,
    required String prchOrdrSplitId,
    required String categoryId,
  }) async {
    var url =
        "$baseAppUrl/apis/bidding/get-all-deliverydates/$orderGroupId?prchOrdrSplitId=$prchOrdrSplitId&categoryId=$categoryId";
    try {
      Response response = await dio.get(url);
      var json = response.data;
      safePrint(json);
      var res = DeliveryDatesRes.fromJson(json);
      return res;
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to get delivery dates. Please try again later.");
    }
  }

  Future<CommonResponse> notify(int prchOrdrOffrId) async {
    var url = "$baseAppUrl/apis/bidding/send-nego/$prchOrdrOffrId";
    try {
      Response response = await dio.post(url);
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to notify. Please try again later.");
    }
  }

  Future<CommonResponse> notifyAction({
    required int prchOrdrId,
    required String statusCd,
    required int offerId,
  }) async {
    var url = "$baseAppUrl/apis/bidding/notify-action";
    try {
      Response response = await dio.post(
        url,
        data: {
          "prchOrdrId": prchOrdrId,
          "statusCd": statusCd,
          "offerId": offerId,
        },
      );
      var json = response.data;
      safePrint(json);
      return CommonResponse.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to send notification action. Please try again later.");
    }
  }

  /// Create round off discount
  Future<RoundOffDiscountRes> createRoundOffDiscount({
    required String orderGroupId,
    required double discountRoundOff,
    required List<int> offerId,
  }) async {
    var url = "$baseAppUrl/apis/bidding/prch-ordr-offr-grp/create-round-off";
    try {
      Response response = await dio.post(
        url,
        data: RoundOffDiscountReq(
          offerIds: offerId,
          discountRoundOff: discountRoundOff.toInt(),
        ).toJson(),
      );
      var json = response.data;
      return RoundOffDiscountRes.fromJson(json);
    } on DioException catch (error) {
      safePrint(error.response.toString());
      throw Exception(error.response?.data?["status_description"] ??
          "Unable to create round off discount. Please try again later.");
    }
  }
}
