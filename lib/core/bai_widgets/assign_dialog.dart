import 'dart:developer';

import 'package:connectone/bai_blocs/assign/cubit/assign_cubit.dart';
import 'package:connectone/bai_models/assign_req.dart';
import 'package:connectone/bai_models/bai_products_res.dart' as bpr;
import 'package:connectone/bai_models/vendor_list_res.dart';
import 'package:connectone/core/bai_widgets/user_profile_dialog.dart';
import 'package:connectone/core/bai_widgets/app_loader.dart';
import 'package:connectone/core/bai_widgets/filter_multi_select.dart';
import 'package:connectone/core/network/network_controller.dart';
import 'package:connectone/core/utils/colors.dart';
import 'package:connectone/core/utils/extensions.dart';
import 'package:connectone/core/utils/tools.dart';
import 'package:connectone/old_blocs/offline_stocks/offline_stocks_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:loader_overlay/loader_overlay.dart';

class AssignDialog extends StatefulWidget {
  const AssignDialog({
    Key? key,
    required this.content,
    required this.reload,
    required this.changeStatus,
    required this.date,
    required this.categoryId,
  }) : super(key: key);

  final bpr.Content content;
  final Function reload;
  final Function changeStatus;
  final DateTime date;
  final String? categoryId;

  @override
  State<AssignDialog> createState() => _AssignDialogState();
}

class _AssignDialogState extends State<AssignDialog> {
  var selectedItem = 0;
  var selectedUser = -1;

  var controller = TextEditingController();

  bool isSubmitted = true;

  List<String> districts = [];
  List<String> selectedDistricts = [];
  List<String> distances = [];
  String? selectedDistance;
  AssignVendor? selectedVendor;
  List<AssignVendor> selectedVendors = [];
  List<String> alreadyAssignedIds = [];

  bool selectAll = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // _setItems();
    getVendors();
  }

  void _scrollToBottom() {
    _scrollController.animateTo(
      _scrollController.position.maxScrollExtent,
      duration: const Duration(seconds: 1),
      curve: Curves.easeOut,
    );
  }

  getVendors() {
    context.read<AssignCubit>().getVendors(
          prchOrdrSplitId: widget.categoryId ?? "",
          deliveryDate: widget.date,
          district: selectedDistricts.join(","),
          name: controller.text,
        );
  }

  void _showConfirmationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Confirmation',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: const Text(
              'Vendors have been successfully assigned.\n\nWould you like to post this Material Request now?'),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                widget.reload();
                Navigator.pop(context);
                Navigator.pop(context);
              },
              child: Text(
                'Cancel',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
            ),
            TextButton(
              onPressed: () async {
                try {
                  var api = NetworkController();
                  var res = await api.changeStatus(
                      widget.content.orderGroupId?.toString() ?? "0");
                  alert(res.statusDescription);
                  context
                      .read<OfflineMainBloc>()
                      .add(const RefreshOfflineStocks());
                  Navigator.pop(context);
                  Navigator.pop(context);
                } catch (e) {
                  alert("Error while posting: $e");
                }
              },
              child: Text(
                'Yes',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  bool isAlreadyAssigned(int vendorId) {
    return alreadyAssignedIds.contains(vendorId.toString());
  }

  @override

  /// Builds the Assign Dialog.
  ///
  /// This widget is responsible for rendering the assign dialog, which is used to
  /// assign vendors to a particular order.
  ///
  /// The dialog has a tabbed interface with options to select vendors by district,
  /// distance or name. The selected vendors are displayed in a list view with a
  /// checkbox to select/deselect vendors.
  ///
  /// The dialog also has a 'Show more' button at the bottom, which is used to
  /// load more vendors. The 'Show more' button is only visible if there are more
  /// vendors to be loaded.
  ///
  /// The dialog also has a 'NEXT' button at the bottom, which is used to submit
  /// the selected vendors. The 'NEXT' button is only visible if the user has
  /// selected some vendors.
  ///
  /// The dialog also has a 'INVITE' button at the bottom, which is used to invite
  /// the selected vendors. The 'INVITE' button is only visible if the user has
  /// submitted the selected vendors.
  ///
  /// The dialog also displays a confirmation dialog after the user has submitted
  /// the selected vendors. The confirmation dialog asks the user if they want
  /// to post the material request after inviting the vendors.
  Widget build(BuildContext context) {
    return AppLoader(
      color: Colors.white10,
      child: BlocConsumer<AssignCubit, AssignState>(
        listener: (context, state) {
          if (state is AssignSuccess) {
            Future.delayed(
                Duration.zero, () => _showConfirmationDialog(context));
          }
          if (state is AssignError) {
            properAlert(state.error);
          }
          if (state is AssignLoaded) {
            setState(() {
              selectedVendors.clear();
              var alreadyAssignedVendors =
                  state.vendorList.alreadyAssignedVendors ?? [];
              var vendors = state.vendorList.assignList ?? [];
              alreadyAssignedIds = alreadyAssignedVendors
                  .map((e) => e.vendorId.toString())
                  .toList();
              for (var vendor in vendors) {
                String vendorIdStr = vendor.vendorId.toString().trim();
                bool isContained = false;

                for (var id in alreadyAssignedIds) {
                  // alert("$vendorIdStr $id");
                  if (id == vendorIdStr) {
                    isContained = true;
                    break;
                  }
                }
                if (isContained) {
                  selectedVendors.add(vendor);
                }
              }
              log("${selectedVendors.length.toString()} ${alreadyAssignedVendors.toString()} ${vendors.toString()} ${alreadyAssignedIds.toString()}");
              districts = state.districts?.districts
                      ?.map((e) => e.name.toString())
                      .toList() ??
                  [];
            });
          }
        },
        builder: (context, state) {
          (state is AssignLoading)
              ? context.loaderOverlay.show()
              : context.loaderOverlay.hide();
          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            insetPadding: const EdgeInsets.all(8),
            child: Container(
              padding: const EdgeInsets.all(12.0),
              height: MediaQuery.of(context).size.height * 0.85,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    height: 52,
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.person_pin_rounded,
                          size: 24,
                          color: Colors.white,
                        ),
                        SizedBox(width: 10),
                        Text(
                          'Invite Vendors',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ).withCloseButton(() => Navigator.pop(context)),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            setState(() {
                              selectedItem = 0;
                            });
                          },
                          style: TextButton.styleFrom(
                            backgroundColor:
                                Colors.grey[selectedItem == 0 ? 400 : 300],
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(0),
                            ),
                          ),
                          child: const Text(
                            'By District',
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      // Expanded(
                      //   child: TextButton(
                      //     onPressed: () {
                      //       setState(() {
                      //         selectedItem = 1;
                      //       });
                      //     },
                      //     style: TextButton.styleFrom(
                      //       backgroundColor: Colors.grey[selectedItem == 1 ? 400 : 300],
                      //       shape: RoundedRectangleBorder(
                      //         borderRadius: BorderRadius.circular(0),
                      //       ),
                      //     ),
                      //     child: const Text(
                      //       'By Distance (KM)',
                      //       style: TextStyle(
                      //         color: Colors.black,
                      //         fontSize: 10,
                      //         fontWeight: FontWeight.bold,
                      //       ),
                      //     ),
                      //   ),
                      // ),
                      Expanded(
                        child: TextButton(
                          onPressed: () {
                            setState(() {
                              selectedItem = 2;
                            });
                          },
                          style: TextButton.styleFrom(
                            backgroundColor:
                                Colors.grey[selectedItem == 2 ? 400 : 300],
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(0),
                            ),
                          ),
                          child: const Text(
                            'By Name',
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  if (selectedItem == 0)
                    FilterMultiSelectAssign(
                      labelText: "District",
                      items: districts,
                      selectedValues: selectedDistricts,
                      onChanged: (value) {
                        setState(() {
                          selectedDistricts = value;
                          context.read<AssignCubit>().getVendorsNew(
                                prchOrdrSplitId: widget.categoryId ?? "",
                                deliveryDate: widget.date,
                                district: selectedDistricts.join(","),
                                name: controller.text,
                              );
                        });
                      },
                    ),
                  // if (selectedItem == 0)
                  //   ListView.builder(
                  //       shrinkWrap: true,
                  //       itemCount: districts.length,
                  //       itemBuilder: (context, index) {
                  //         return Row(
                  //           children: [
                  //             Checkbox(
                  //               value: selectedDistricts
                  //                   .contains(districts[index]),
                  //               onChanged: (val) {
                  //                 setState(() {
                  //                   selectedDistricts.contains(districts[index])
                  //                       ? selectedDistricts
                  //                           .remove(districts[index])
                  //                       : selectedDistricts
                  //                           .add(districts[index]);
                  //                   context.read<AssignCubit>().getVendors(
                  //                         projectId:
                  //                             widget.content.projectId ?? "",
                  //                         district: selectedDistricts.join(","),
                  //                         name: controller.text,
                  //                       );
                  //                 });
                  //               },
                  //               activeColor: AppColors.primaryColor,
                  //             ),
                  //             Text(
                  //               districts[index],
                  //               style: const TextStyle(
                  //                 fontSize: 12,
                  //                 fontWeight: FontWeight.bold,
                  //               ),
                  //             )
                  //           ],
                  //         );
                  //       }),
                  if (selectedItem == 1)
                    Expanded(
                      child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: distances.length,
                          itemBuilder: (context, index) {
                            return Row(
                              children: [
                                Radio(
                                  groupValue: selectedDistance,
                                  value: distances[index],
                                  onChanged: (val) {
                                    setState(() {
                                      selectedDistance = val.toString();
                                    });
                                  },
                                ),
                                Text(
                                  distances[index],
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )
                              ],
                            );
                          }),
                    ),
                  if (selectedItem == 2)
                    TextField(
                      controller: controller,
                      decoration: InputDecoration(
                        hintText: 'Search by name',
                        isDense: true,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        suffixIcon: const Icon(Icons.search),
                      ),
                      onChanged: (val) {
                        context.read<AssignCubit>().getVendorsNew(
                              prchOrdrSplitId: widget.categoryId ?? "",
                              deliveryDate: widget.date,
                              district: selectedDistricts.join(","),
                              name: controller.text,
                            );
                      },
                    ),
                  if (isSubmitted) const SizedBox(height: 16),
                  if (isSubmitted)
                    Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey.shade600),
                      ),
                      child: Row(
                        children: [
                          const Expanded(
                            flex: 5,
                            child: Padding(
                              padding: EdgeInsets.all(8.0),
                              child: Text(
                                'Name',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 13,
                                ),
                              ),
                            ),
                          ),
                          const Expanded(
                            flex: 2,
                            child: Padding(
                              padding: EdgeInsets.all(8.0),
                              child: Center(
                                child: Text(
                                  'District',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const Expanded(
                            flex: 2,
                            child: Padding(
                              padding: EdgeInsets.all(8.0),
                              child: Center(
                                child: Text(
                                  'Rating',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 13,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                                child: Row(
                                  children: [
                                    // const Text(
                                    //   'Select',
                                    //   style: TextStyle(
                                    //     fontWeight: FontWeight.bold,
                                    //     fontSize: 12,
                                    //   ),
                                    // ),
                                    Checkbox(
                                      value: selectAll,
                                      onChanged: (val) {
                                        setState(() {
                                          selectAll = !selectAll;
                                          if (selectAll) {
                                            selectedVendors.clear();
                                            for (var i
                                                in (state as AssignLoaded)
                                                    .vendorList
                                                    .assignList!) {
                                              selectedVendors.add(i);
                                            }
                                          } else {
                                            selectedVendors.clear();
                                          }
                                        });
                                      },
                                      visualDensity: VisualDensity.compact,
                                      materialTapTargetSize:
                                          MaterialTapTargetSize.shrinkWrap,
                                      activeColor: AppColors.primaryColor,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (isSubmitted) const SizedBox(height: 4),
                  if (isSubmitted)
                    BlocBuilder<AssignCubit, AssignState>(
                      builder: (context, state) {
                        if (state is AssignLoaded) {
                          var data = state.vendorList;
                          return Expanded(
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                // color: Colors.grey.shade100,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.grey.shade600),
                              ),
                              child: data.assignList?.isNotEmpty ?? false
                                  ? ListView.builder(
                                      controller: _scrollController,
                                      itemCount: data.assignList?.length,
                                      itemBuilder: (context, i) {
                                        var item = data.assignList?[i];
                                        var isBaiMember =
                                            item?.baiMember ?? false;
                                        String gstType =
                                            item?.gstFilingType ?? 'N/A';
                                        return SizedBox(
                                          child: Row(
                                            children: [
                                              Expanded(
                                                flex: 5,
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.all(8.0),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      InkWell(
                                                        onTap: () {
                                                          showDialog(
                                                            context: context,
                                                            builder:
                                                                (BuildContext
                                                                    context) {
                                                              return UserProfileDialog(
                                                                vendorId: item
                                                                    ?.vendorId!,
                                                              );
                                                            },
                                                          );
                                                        },
                                                        child: Text(
                                                          item?.name ?? 'N/A',
                                                          style:
                                                              const TextStyle(
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            fontSize: 14,
                                                            color: AppColors
                                                                .primaryColorOld,
                                                            decoration:
                                                                TextDecoration
                                                                    .underline,
                                                          ),
                                                        ),
                                                      ),
                                                      const SizedBox(height: 4),
                                                      Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          if (isBaiMember)
                                                            Text(
                                                              "BAI Member",
                                                              style: TextStyle(
                                                                fontSize: 12,
                                                                color: Colors
                                                                    .teal
                                                                    .shade900,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                              ),
                                                            ),
                                                          if (isBaiMember)
                                                            const SizedBox(
                                                                height: 4),
                                                          Text(
                                                            "GST: $gstType",
                                                            style: TextStyle(
                                                              fontSize: 12,
                                                              color: Colors.teal
                                                                  .shade900,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.all(8.0),
                                                  child: Center(
                                                    child: Text(
                                                      ((item?.district !=
                                                                      null &&
                                                                  item?.district !=
                                                                      '')
                                                              ? item?.district
                                                              : "N/A") ??
                                                          'N/A',
                                                      style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 12,
                                                      ),
                                                      textAlign:
                                                          TextAlign.center,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.all(8.0),
                                                  child: Center(
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Icon(
                                                          Icons.star,
                                                          color: AppColors
                                                              .primaryColor,
                                                          size: 12,
                                                        ),
                                                        const SizedBox(
                                                            width: 5),
                                                        Text(
                                                          (item?.avgRating ==
                                                                      null ||
                                                                  (item!.avgRating ??
                                                                          "")
                                                                      .isEmpty ||
                                                                  item.avgRating ==
                                                                      "0")
                                                              ? "N/A"
                                                              : item.avgRating!,
                                                          style:
                                                              const TextStyle(
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            fontSize: 12,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 2,
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.all(8.0),
                                                  child: Checkbox(
                                                    value: selectedVendors
                                                        .contains(item),
                                                    onChanged: (bool? value) {
                                                      setState(
                                                        () {
                                                          if (value == true) {
                                                            selectedVendors.add(
                                                                item ??
                                                                    AssignVendor());
                                                          } else {
                                                            if (isAlreadyAssigned(
                                                                item?.vendorId ??
                                                                    0)) {
                                                              alert(
                                                                  "You have already assigned this vendor, so you can't remove it.");
                                                              return;
                                                            }
                                                            selectedVendors
                                                                .remove(item);
                                                          }
                                                        },
                                                      );
                                                    },
                                                    activeColor:
                                                        AppColors.primaryColor,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                      shrinkWrap: true,
                                    )
                                  : const SizedBox(
                                      width: double.infinity,
                                      child: Center(
                                          child: Text("No vendors found")),
                                    ),
                            ),
                          );
                        } else {
                          return Container();
                        }
                      },
                    ),

                  GestureDetector(
                    onTap: () {
                      _scrollToBottom();
                    },
                    child: Container(
                      width: double.infinity,
                      alignment: Alignment.center,
                      child: const Padding(
                        padding: EdgeInsets.fromLTRB(6, 12, 6, 6),
                        child: Text(
                          'Show more',
                          textAlign: TextAlign.right,
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        setState(() {
                          if (!isSubmitted) {
                            getVendors();
                          } else {
                            selectedVendors.removeWhere((vendor) =>
                                alreadyAssignedIds
                                    .toSet()
                                    .contains(vendor.vendorId.toString()));
                            if (selectedVendors.isEmpty) {
                              alert("Please select vendors to invite.");
                              return;
                            }
                            AssignReq req = AssignReq(
                              customerId: selectedVendors.first.customerId,
                              vendorsId: selectedVendors
                                  .map((x) => x.customerId ?? 0)
                                  .toList(),
                              insertedCustomerId: int.parse(getCustomerId()),
                              insertedVendorId: int.parse(getVendorId()),
                              purchaseOrderId:
                                  widget.content.prchOrdrId?.toInt() ?? 0,
                              orderGrpId:
                                  widget.content.orderGroupId?.toInt() ?? 0,
                              deliveryDate: widget.date,
                              prchOrdrSplitId:
                                  int.tryParse(widget.categoryId ?? ""),
                            );
                            context.read<AssignCubit>().assignVendor(req);
                          }
                          // isSubmitted = true;
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 50,
                          vertical: 15,
                        ),
                      ),
                      child: Text(
                        isSubmitted
                            ? selectedVendors.isNotEmpty
                                ? 'INVITE - ${selectedVendors.length} Vendors'
                                : 'INVITE'
                            : 'NEXT',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  isSelected(AssignVendor item) {
    return selectedVendors.contains(item);
  }
}
